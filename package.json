{"name": "AgriLala_Api_Hiral", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon index", "dev": "node index"}, "repository": {"type": "git", "url": "git+https://github.com/DJDhvanit28/AgriLala_Api_Hiral.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/DJDhvanit28/AgriLala_Api_Hiral.git/issues"}, "homepage": "https://github.com/DJDhvanit28/AgriLala_Api_Hiral.git#readme", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-fileupload": "^1.4.0", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.0", "moment": "^2.29.4", "nodemailer": "^6.10.0", "nodemon": "^3.1.0", "pg": "^8.11.5", "sequelize": "^6.31.1"}, "devDependencies": {"sequelize-cli": "^6.6.0"}}