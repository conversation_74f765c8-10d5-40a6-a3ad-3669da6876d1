-- Migration to add mobile and is_mobile_verified fields to user table
-- Run this SQL script on your database

-- Add mobile field to user table
ALTER TABLE "user" 
ADD COLUMN mobile VARCHAR(15) UNIQUE;

-- Add is_mobile_verified field to user table
ALTER TABLE "user" 
ADD COLUMN is_mobile_verified BOOLEAN DEFAULT false;

-- Create index on mobile field for better performance
CREATE INDEX idx_user_mobile ON "user"(mobile);

-- Create index on is_mobile_verified field for better performance
CREATE INDEX idx_user_mobile_verified ON "user"(is_mobile_verified);

-- Optional: Update existing users to have is_mobile_verified = false (already default)
-- UPDATE "user" SET is_mobile_verified = false WHERE is_mobile_verified IS NULL;
