require("dotenv").config();
const express = require("express");
const cors = require("cors");
const fileUpload = require("express-fileupload");
const routes = require("./src/routes");
const app = express();
app.set("trust proxy", true);
app.use(express.json());
const { PORT } = require("./src/constant");
require("./src/db").connectDB();
app.use(cors());

app.use(fileUpload());
app.use(routes);
app.use((err, req, res, next) => {
  console.error(err);
  res.status(500).json({
    status: false,
    statusCode: err.statusCode || 500,
    message: err.message,
  });
});
app.use(express.static("public"));

app.use("/", (req, res) => {
  res.send({ status: false, statusCode: 404, message: "Path Not Found" });
});

app.listen(PORT, () => {
  console.log("Server listening on PORT:", `https://localhost:${PORT}`);
});
