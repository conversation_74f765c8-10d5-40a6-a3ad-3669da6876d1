const cloudinary = require("cloudinary").v2;

cloudinary.config({
  cloud_name: "dypnsvtlc",
  api_key: "948356158582856",
  api_secret: "wV2fFZq6SGrb8-NVVDfABKfgL3s",
});

async function uploadImage(imageFile) {
  const b64 = Buffer.from(imageFile.data).toString("base64");
  let dataURI = "data:" + imageFile.mimetype + ";base64," + b64;
  const res = await cloudinary.uploader.upload(dataURI);
  return res.secure_url;
}
module.exports = uploadImage;
