const express = require("express");
const router = express.Router();

router.use("/", require("./commonRoutes"));
router.use("/company", require("./companyRoutes"));
router.use("/staff", require("./staffRoutes"));
router.use("/dealer-distributor", require("./dealerDistributorRoutes"));
router.use("/post", require("./postRoutes"));
router.use("/subscription", require("./subscriptionRoutes"));
router.use("/member", require("./memberRoutes"));
router.use("/irregularity", require("./irregularityRoutes"));
router.use("/product", require("./productRoutes"));
router.use("/hiring-post", require("./hiringPostRoutes"));
router.use("/company-lead", require("./companyLeadRoutes"));
router.use("/company-post", require("./companyPostRoutes"));

module.exports = router;
