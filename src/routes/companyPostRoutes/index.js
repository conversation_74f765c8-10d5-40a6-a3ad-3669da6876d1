const express = require("express");
const {
  getPostList,
  addPost,
  addPostLike,
  addPostComment,
  addPostShare,
  getPostLikeList,
  getPostCommentList,
  getPostDetailById,
} = require("../../controllers/company_post.controller");
const router = express.Router();
let {
  validateAddPost,
  validatePostLike,
  validatePostComment,
  validatePostShare,
} = require("../../middlewares/validators/company_post");
let auth = require("../../middlewares/auth.middleware");

router.get("/", auth(), getPostList);
router.get("/:company_post_id", auth(), getPostDetailById);
router.post("/", auth(), validateAddPost, addPost);
router.post("/like", auth(), validatePostLike, addPostLike);
router.post("/comment", auth(), validatePostComment, addPostComment);
router.post("/share", auth(), validatePostShare, addPostShare);
router.get("/like/:company_post_id", auth(), getPostLikeList);
router.get("/comment/:company_post_id", auth(), getPostCommentList);
module.exports = router;
