const express = require("express");
const {
  registerCompany,
  checkAdminApproval,
  getCompanyList,
  addCompanyExpenses,
  getCompanyExpensesDetails,
  approveCompanyByAdmin,
} = require("../../controllers/company.controller");
const router = express.Router();
let {
  validateCompany,
  validateAddExpense,
} = require("../../middlewares/validators/company");
let auth = require("../../middlewares/auth.middleware");

router.post("/register-company", validateCompany, registerCompany);
router.get("/check-approval", auth(), checkAdminApproval);
router.get("/company-list", auth(), getCompanyList);

router.post("/add-expenses", auth(), validateAddExpense, addCompanyExpenses);
router.get(
  "/get-expenses/:company_id/:expense_date",
  auth(),
  getCompanyExpensesDetails
);
router.get("/approve-by-admin", auth(), approveCompanyByAdmin);

module.exports = router;
