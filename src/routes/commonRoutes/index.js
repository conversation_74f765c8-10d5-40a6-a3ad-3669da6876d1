const express = require("express");
const {
  login,
  sendOTP,
  getLocationDataByPincode,
  verifyOTP,
  setNewPassword,
  getUserDetails,
} = require("../../controllers/auth.controller");
const router = express.Router();
let {
  validateLogin,
  validateSendOTP,
  validateVerifyOTP,
} = require("../../middlewares/validators/auth");
let { validateFollowing } = require("../../middlewares/validators/follow");
const {
  uploadMedia,
  multipleUploadMedia,
  getStateList,
  getDistrictList,
  getTalukaList,
} = require("../../controllers/common.controller");
const {
  addFollowing,
  unfollowUser,
} = require("../../controllers/follow.controller");
let auth = require("../../middlewares/auth.middleware");

router.post("/login/:type", validateLogin, login);
router.post("/send-otp/:type", validateSendOTP, sendOTP);
router.post("/verify-otp/:type", validateVerifyOTP, verifyOTP);
router.post("/create-password/:type", validateLogin, setNewPassword);
router.get("/location/:pincode", getLocationDataByPincode);
router.post("/upload-media", uploadMedia);
router.post("/multiple/upload-media", multipleUploadMedia);
router.post("/follow", auth(), validateFollowing, addFollowing);
router.post("/unfollow", auth(), validateFollowing, unfollowUser);
router.get("/state", getStateList);
router.get("/district", getDistrictList);
router.get("/taluka", getTalukaList);
router.get("/user-details", auth(), getUserDetails);

module.exports = router;
