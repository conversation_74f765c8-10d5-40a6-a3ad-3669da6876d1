const express = require("express");
const {
  addMember,
  updateMember,
  getTeamMemberList,
  deleteMember,
} = require("../../controllers/member.controller");
const router = express.Router();
let { validateAddMember } = require("../../middlewares/validators/member");
let auth = require("../../middlewares/auth.middleware");

router.post("/", auth(), validateAddMember, addMember);
router.patch("/:team_member_id", auth(), validateAddMember, updateMember);
router.get("/", auth(), getTeamMemberList);
router.delete("/:team_member_id", auth(), deleteMember);

module.exports = router;
