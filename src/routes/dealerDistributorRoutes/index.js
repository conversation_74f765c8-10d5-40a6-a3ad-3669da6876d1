const express = require("express");
const {
  registerDealerDistributor,
} = require("../../controllers/dealer_distributor.controller");
const {
  addBuyerTool,
  buyerRequirementsList,
  addBuyerRequirementLike,
  addBuyerRequirementCall,
  getBuyerFieldList,
  addBuyerRequirementComment,
  getBuyerRequirementCommentList,
  getBuyerRequirementLikeList,
  activeInactiveBuyerTool,
  deleteBuyerTool,
} = require("../../controllers/buyer_tool.controller");
const router = express.Router();
let {
  validateDealerDistributor,
  validateBuyerRequirements,
  validateBuyerRequirementLike,
  validateBuyerRequirementCall,
  validateBuyerRequirementComment,
  validateBuyerRequirementActiveInactive,
} = require("../../middlewares/validators/dealer_distributor");
let auth = require("../../middlewares/auth.middleware");

router.post(
  "/register-firm",
  validateDealerDistributor,
  registerDealerDistributor
);
router.post(
  "/post-requirements",
  auth(),
  validateBuyerRequirements,
  addBuyerTool
);
router.get("/buyer-requirements/list", auth(), buyerRequirementsList);
router.post(
  "/buyer-requirements/like",
  auth(),
  validateBuyerRequirementLike,
  addBuyerRequirementLike
);
router.post(
  "/buyer-requirements/call",
  auth(),
  validateBuyerRequirementCall,
  addBuyerRequirementCall
);

router.get("/buyer-field/list", auth(), getBuyerFieldList);

router.post(
  "/buyer-requirements/comment",
  auth(),
  validateBuyerRequirementComment,
  addBuyerRequirementComment
);
router.get(
  "/buyer-requirements/like/:buyer_requirement_id",
  auth(),
  getBuyerRequirementLikeList
);
router.get(
  "/buyer-requirements/comment/:buyer_requirement_id",
  auth(),
  getBuyerRequirementCommentList
);
router.patch(
  "/buyer-requirements/active-inactive/:buyer_requirement_id",
  auth(),
  validateBuyerRequirementActiveInactive,
  activeInactiveBuyerTool
);
router.delete(
  "/buyer-requirements/delete/:buyer_requirement_id",
  auth(),
  deleteBuyerTool
);
module.exports = router;
