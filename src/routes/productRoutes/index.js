const express = require("express");
const {
  getProductList,
  addProduct,
  addProductLike,
  addProductComment,
  addProductShare,
  getProductLikeList,
  getProductCommentList,
  getProductDetailById,
  editProduct,
  deleteProduct,
} = require("../../controllers/product.controller");
const router = express.Router();
let {
  validateAddProduct,
  validateProductLike,
  validateProductComment,
  validateProductShare,
} = require("../../middlewares/validators/product");
let auth = require("../../middlewares/auth.middleware");

router.get("/", auth(), getProductList);
router.get("/:product_id", auth(), getProductDetailById);
router.post("/", auth(), validateAddProduct, addProduct);
router.post("/like", auth(), validateProductLike, addProductLike);
router.post("/comment", auth(), validateProductComment, addProductComment);
router.post("/share", auth(), validateProductShare, addProductShare);
router.get("/like/:product_id", auth(), getProductLikeList);
router.get("/comment/:product_id", auth(), getProductCommentList);
router.put("/:product_id", auth(), editProduct);
router.delete("/:product_id", auth(), deleteProduct);
module.exports = router;
