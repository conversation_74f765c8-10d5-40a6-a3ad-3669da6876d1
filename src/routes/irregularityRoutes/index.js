const express = require("express");
const {
  addIrregularity,
  irregularityList,
  searchIrregularityByName,
  addIrregularityComment,
  getIrregularityLikeList,
  getIrregularityCommentList,
  addIrregularityLike,
  getIrregularityById,
  editIrregularity,
  deleteIrregularity,
} = require("../../controllers/irregularity.controller");
const router = express.Router();
let {
  validateAddIrregularity,
  validateIrregularityLike,
  validateIrregularityComment,
} = require("../../middlewares/validators/irregularity");
let auth = require("../../middlewares/auth.middleware");

router.post("/", auth(), validateAddIrregularity, addIrregularity);
router.get("/", auth(), validateAddIrregularity, irregularityList);
router.get("/search-by-name", auth(), searchIrregularityByName);
router.post("/like", auth(), validateIrregularityLike, addIrregularityLike);
router.post(
  "/comment",
  auth(),
  validateIrregularityComment,
  addIrregularityComment
);
router.get("/like/:irregularity_id", auth(), getIrregularityLikeList);
router.get("/comment/:irregularity_id", auth(), getIrregularityCommentList);
router.get("/:irregularity_id", auth(), getIrregularityById);
router.patch("/:irregularity_id", auth(), editIrregularity);
router.delete("/:irregularity_id", auth(), deleteIrregularity);
module.exports = router;
