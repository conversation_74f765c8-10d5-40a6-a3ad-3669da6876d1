const express = require("express");
const {
  getHiringPostList,
  addHiringPost,
  addHiringPostLike,
  addHiringPostComment,
  addHiringPostShare,
  getHiringPostLikeList,
  getHiringPostCommentList,
  getHiringPostDetailById,
  editHiringPost,
  deleteHiringPost,
} = require("../../controllers/hiring_post.controller");
const router = express.Router();
let {
  validateAddHiringPost,
  validateHiringPostInterest,
  validateHiringPostComment,
  validateHiringPostShare,
} = require("../../middlewares/validators/hiring_post");
let auth = require("../../middlewares/auth.middleware");

router.get("/", auth(), getHiringPostList);
router.get("/:hiring_post_id", auth(), getHiringPostDetailById);
router.post("/", auth(), validateAddHiringPost, addHiringPost);
router.post("/interest", auth(), validateHiringPostInterest, addHiringPostLike);
router.post(
  "/comment",
  auth(),
  validateHiringPostComment,
  addHiringPostComment
);
router.post("/share", auth(), validateHiringPostShare, addHiringPostShare);
router.get("/interest/:hiring_post_id", auth(), getHiringPostLikeList);
router.get("/comment/:hiring_post_id", auth(), getHiringPostCommentList);
router.put("/:hiring_post_id", auth(), editHiringPost);
router.delete("/:hiring_post_id", auth(), deleteHiringPost);
module.exports = router;
