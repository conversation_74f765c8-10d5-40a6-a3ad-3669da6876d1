const express = require("express");
const {
  getCompanyLeadPostList,
  addCompanyLeadPost,
  addCompanyLeadPostLike,
  addCompanyLeadPostComment,
  addCompanyLeadPostShare,
  getCompanyLeadPostLikeList,
  getCompanyLeadPostCommentList,
  getCompanyLeadPostDetailById,
} = require("../../controllers/company_lead.controller");
const router = express.Router();
let {
  validateAddCompanyLeadPost,
  validateCompanyLeadPostLike,
  validateCompanyLeadPostComment,
  validateCompanyLeadPostShare,
} = require("../../middlewares/validators/company_lead");
let auth = require("../../middlewares/auth.middleware");

router.get("/", auth(), getCompanyLeadPostList);
router.get("/:company_lead_id", auth(), getCompanyLeadPostDetailById);
router.post("/", auth(), validateAddCompanyLeadPost, addCompanyLeadPost);
router.post(
  "/like",
  auth(),
  validateCompanyLeadPostLike,
  addCompanyLeadPostLike
);
router.post(
  "/comment",
  auth(),
  validateCompanyLeadPostComment,
  addCompanyLeadPostComment
);
router.post(
  "/share",
  auth(),
  validateCompanyLeadPostShare,
  addCompanyLeadPostShare
);
router.get("/like/:company_lead_id", auth(), getCompanyLeadPostLikeList);
router.get("/comment/:company_lead_id", auth(), getCompanyLeadPostCommentList);
module.exports = router;
