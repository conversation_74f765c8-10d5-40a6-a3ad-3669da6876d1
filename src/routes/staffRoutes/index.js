const express = require("express");
const {
  registerStaff,
  membersList,
  addStaffLocation,
  staffLocationList,
  getStaffCompanyDetail,
  addStaffCompany,
  removeStaffCompany,
  approveStaffCompany,
  staffProfile,
  editStaffProfile,
  staffProfileList,
} = require("../../controllers/staff.controller");
const router = express.Router();
let { validateStaff } = require("../../middlewares/validators/staff");
let auth = require("../../middlewares/auth.middleware");

router.post("/register-staff", validateStaff, registerStaff);
router.get("/list", auth(), membersList);
router.post("/add-location", auth(), addStaffLocation);
router.get("/location", auth(), staffLocationList);
router.get("/company/detail/:staff_id", auth(), getStaffCompanyDetail);
router.post("/company/add", auth(), addStaffCompany);
router.get("/company/remove/:staff_id", auth(), removeStaffCompany);
router.get(
  "/company/approve/:staff_id/:company_id",
  auth(),
  approveStaffCompany
);
router.get("/profile", auth(), staffProfile);
router.post("/profile", auth(), editStaffProfile);
router.get("/bio", auth(), staffProfileList);

module.exports = router;
