const express = require("express");
const {
  getPostList,
  addPost,
  addPostLike,
  addPostComment,
  addPostShare,
  getPostLikeList,
  getPostCommentList,
  getPostDetailById,
} = require("../../controllers/post.controller");
const router = express.Router();
let {
  validateAddPost,
  validatePostLike,
  validatePostComment,
  validatePostShare,
} = require("../../middlewares/validators/post");
let auth = require("../../middlewares/auth.middleware");

router.get("/", auth(), getPostList);
router.get("/:post_id", auth(), getPostDetailById);
router.post("/", auth(), validateAddPost, addPost);
router.post("/like", auth(), validatePostLike, addPostLike);
router.post("/comment", auth(), validatePostComment, addPostComment);
router.post("/share", auth(), validatePostShare, addPostShare);
router.get("/like/:post_id", auth(), getPostLikeList);
router.get("/comment/:post_id", auth(), getPostCommentList);
module.exports = router;
