const express = require("express");
const {
  getSubscriptionList,
  userSubscriptionDetails,
  addUserSubscription,
} = require("../../controllers/subscription.controller");
const router = express.Router();
let auth = require("../../middlewares/auth.middleware");

router.get("/", auth(), getSubscriptionList);
router.get("/user", auth(), userSubscriptionDetails);
router.post("/add-user", auth(), addUserSubscription);

module.exports = router;
