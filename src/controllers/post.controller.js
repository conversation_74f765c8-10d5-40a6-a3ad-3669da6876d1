let { checkValidation } = require("../validation");
const { tables, sequelize } = require("../db/index");
const { asyncHandler } = require("../utils/catchError");
const AppError = require("../utils/appError");

exports.getPostList = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let followingData = await tables.Follow.findAll({
    where: { follower_id: id, is_deleted: false },
  });

  const followingIds = followingData.map((follow) => follow.following_id);
  followingIds.push(id);
  let data = await tables.Post.findAll({
    attributes: [
      "id",
      "details",
      "created_at",
      "user_id",
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM post_like WHERE post_id = post.id AND post_like.is_liked = true AND post_like.is_deleted = false)"
        ),
        "like_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM post_comment WHERE post_id = post.id AND post_comment.is_deleted = false)"
        ),
        "comment_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM post_share WHERE post_id = post.id AND post_share.is_deleted = false)"
        ),
        "share_count",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN EXISTS (
          SELECT is_liked
          FROM post_like
          WHERE user_id = ${id} AND post_id = post.id
        ) THEN 
          CASE WHEN (
            SELECT is_liked
            FROM post_like
            WHERE user_id = ${id} AND post_id = post.id
          ) = true THEN 1 ELSE 0 END
        ELSE 0 END)
      `),
        "is_like",
      ],
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = post.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN post.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    where: { user_id: followingIds, is_deleted: false },
    include: [
      { model: tables.User, attributes: ["name", "email", "profile_link"] },
      { model: tables.PostMedia, attributes: ["media_type", "media_link"] },
    ],
    order: [["id", "desc"]],
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});
exports.getPostDetailById = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let { post_id } = req.params;
  let data = await tables.Post.findOne({
    attributes: [
      "id",
      "details",
      "created_at",
      "user_id",
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM post_like WHERE post_id = post.id AND post_like.is_liked = true AND post_like.is_deleted = false)"
        ),
        "like_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM post_comment WHERE post_id = post.id AND post_comment.is_deleted = false)"
        ),
        "comment_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM post_share WHERE post_id = post.id AND post_share.is_deleted = false)"
        ),
        "share_count",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN EXISTS (
          SELECT is_liked
          FROM post_like
          WHERE user_id = ${id} AND post_id = post.id
        ) THEN 
          CASE WHEN (
            SELECT is_liked
            FROM post_like
            WHERE user_id = ${id} AND post_id = post.id
          ) = true THEN 1 ELSE 0 END
        ELSE 0 END)
      `),
        "is_like",
      ],
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = post.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN post.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    where: { id: post_id, is_deleted: false },
    include: [
      { model: tables.User, attributes: ["name", "email", "profile_link"] },
      { model: tables.PostMedia, attributes: ["media_type", "media_link"] },
    ],
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});
exports.addPost = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  req.body.user_id = id;
  let { post_media } = req.body;
  let postData = await tables.Post.create(req.body);
  let postMediaArray = [];
  for (let i = 0; i < post_media.length; i++) {
    postMediaArray.push({
      media_link: post_media[i].media_link,
      user_id: id,
      post_id: postData.id,
      media_type: post_media[i].media_type,
    });
  }
  await tables.PostMedia.bulkCreate(postMediaArray);
  return res.send({
    status: true,
    statusCode: 200,
    message: "post uploaded",
  });
});

exports.addPostLike = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { post_id, is_liked } = req.body;
  let data = await tables.PostLike.findOne({
    where: { user_id: id, post_id },
  });
  if (!data) {
    await tables.PostLike.create({ user_id: id, post_id, is_liked });
  } else {
    await tables.PostLike.update(
      { is_liked },
      { where: { user_id: id, post_id } }
    );
  }
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});
exports.addPostComment = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { post_id, comment } = req.body;

  let postData = await tables.PostComment.create({
    user_id: id,
    post_id,
    comment,
  });
  let data = await tables.PostComment.findOne({
    where: { id: postData?.id },
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.addPostShare = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { post_id } = req.body;

  await tables.PostShare.create({ user_id: id, post_id, is_shared: true });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});

exports.getPostLikeList = asyncHandler(async (req, res) => {
  let { post_id } = req.params;
  let { id } = req.user;
  const likeCountQuery = await tables.PostLike.count({
    where: {
      post_id: post_id,
      is_liked: true,
      is_deleted: false,
    },
  });
  const commentCountQuery = await tables.PostComment.count({
    where: {
      post_id: post_id,
      is_deleted: false,
    },
  });
  const isLikeQuery = await tables.PostLike.findOne({
    where: { post_id, user_id: id },
  });
  let data = await tables.PostLike.findAll({
    where: { post_id, is_liked: true },
    attributes: [
      "id",
      "user_id",
      "post_id",
      "created_at",
      "is_liked",
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = post_like.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN post_like.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: {
      like_count: likeCountQuery,
      comment_count: commentCountQuery,
      is_like: isLikeQuery ? isLikeQuery.is_liked : false,
      data,
    },
  });
});

exports.getPostCommentList = asyncHandler(async (req, res) => {
  let { post_id } = req.params;
  let { id } = req.user;
  const likeCountQuery = await tables.PostLike.count({
    where: {
      post_id: post_id,
      is_liked: true,
      is_deleted: false,
    },
  });
  const commentCountQuery = await tables.PostComment.count({
    where: {
      post_id: post_id,
      is_deleted: false,
    },
  });
  const isLikeQuery = await tables.PostLike.findOne({
    where: { post_id, user_id: id },
  });

  let data = await tables.PostComment.findAll({
    where: { post_id },
    attributes: [
      "id",
      "user_id",
      "post_id",
      "created_at",
      "comment",
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = post_comment.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN post_comment.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: {
      like_count: likeCountQuery,
      comment_count: commentCountQuery,
      is_like: isLikeQuery ? isLikeQuery.is_liked : false,
      data,
    },
  });
});
