let { checkValidation } = require("../validation");
const { tables, sequelize } = require("../db/index");
const { asyncHandler } = require("../utils/catchError");
const AppError = require("../utils/appError");
const { Op } = require("sequelize");

exports.addIrregularity = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  req.body.inform_by = id;
  await tables.Irregularity.create(req.body);

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});

exports.irregularityList = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let where = { is_deleted: false };

  const { name, city, state, district, taluka, user_type } = req.query;
  if (name) {
    where.name = {
      [Op.iLike]: `%${name}%`,
    };
  }
  if (city) {
    where.city = {
      [Op.iLike]: `%${city}%`,
    };
  }
  if (state) {
    where.state = {
      [Op.iLike]: `%${state}%`,
    };
  }
  if (district) {
    where.district = {
      [Op.iLike]: `%${district}%`,
    };
  }
  if (taluka) {
    where.taluka = {
      [Op.iLike]: `%${taluka}%`,
    };
  }
  if (user_type) {
    where.user_type = user_type;
  }
  const data = await tables.Irregularity.findAll({
    attributes: [
      "id",
      "user_id",
      "name",
      "mobile",
      "inform_by",
      "firm_name",
      "user_type",
      "user_photo",
      "city",
      "district",
      "taluka",
      "state",
      "note",
      "is_deleted",
      "created_at",
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM irregularity_like WHERE irregularity_id = irregularity.id AND irregularity_like.is_liked = true AND irregularity_like.is_deleted = false)"
        ),
        "like_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM irregularity_comment WHERE irregularity_id = irregularity.id AND irregularity_comment.is_deleted = false)"
        ),
        "comment_count",
      ],
      [
        sequelize.literal(`
              (SELECT CASE WHEN EXISTS (
                SELECT is_liked
                FROM irregularity_like
                WHERE user_id = ${id} AND irregularity_id = irregularity.id
              ) THEN 
                CASE WHEN (
                  SELECT is_liked
                  FROM irregularity_like
                  WHERE user_id = ${id} AND irregularity_id = irregularity.id
                ) = true THEN 1 ELSE 0 END
              ELSE 0 END)
            `),
        "is_like",
      ],
      [
        sequelize.literal(`
            (SELECT CASE WHEN EXISTS (
              SELECT is_deleted
              FROM follow
              WHERE follower_id = ${id} AND following_id = irregularity.user_id AND is_deleted = false
            ) THEN 1 ELSE 0 END)
          `),
        "is_following",
      ],
      [
        sequelize.literal(`
              (SELECT CASE WHEN irregularity.inform_by = ${id} THEN 1 ELSE 0 END)
            `),
        "is_current_user",
      ],
    ],

    include: [
      {
        model: tables.User,
        as: "inform_by_user",
        attributes: ["id", "name"],
      },
    ],
    where,
    order: [["id", "desc"]],
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.searchIrregularityByName = asyncHandler(async (req, res) => {
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.addIrregularityComment = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { irregularity_id, comment } = req.body;
  let checkIrregularity = await tables.Irregularity.findOne({
    where: { id: irregularity_id },
  });
  if (!checkIrregularity) {
    return res.send({
      status: true,
      statusCode: 404,
      message: "irregularity id not found!",
    });
  }
  let irregularityData = await tables.IrregularityComment.create({
    user_id: id,
    irregularity_id: irregularity_id,
    comment,
  });
  let data = await tables.IrregularityComment.findOne({
    where: { id: irregularityData?.id },
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.getIrregularityLikeList = asyncHandler(async (req, res) => {
  let { irregularity_id } = req.params;
  let { id } = req.user;
  const likeCountQuery = await tables.IrregularityLike.count({
    where: {
      irregularity_id: irregularity_id,
      is_liked: true,
      is_deleted: false,
    },
  });
  const commentCountQuery = await tables.IrregularityComment.count({
    where: {
      irregularity_id: irregularity_id,
      is_deleted: false,
    },
  });
  const isLikeQuery = await tables.IrregularityLike.findOne({
    where: { irregularity_id, user_id: id },
  });
  let data = await tables.IrregularityLike.findAll({
    where: { irregularity_id, is_liked: true },
    attributes: [
      "id",
      "user_id",
      "irregularity_id",
      "created_at",
      "is_liked",
      [
        sequelize.literal(`
          (SELECT CASE WHEN EXISTS (
            SELECT is_deleted
            FROM follow
            WHERE follower_id = ${id} AND following_id = irregularity_like.user_id AND is_deleted = false
          ) THEN 1 ELSE 0 END)
        `),
        "is_following",
      ],
      [
        sequelize.literal(`
            (SELECT CASE WHEN irregularity_like.user_id = ${id} THEN 1 ELSE 0 END)
          `),
        "is_current_user",
      ],
    ],
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: {
      like_count: likeCountQuery,
      comment_count: commentCountQuery,
      is_like: isLikeQuery ? isLikeQuery.is_liked : false,
      data,
    },
  });
});

exports.getIrregularityCommentList = asyncHandler(async (req, res) => {
  let { irregularity_id } = req.params;
  let { id } = req.user;
  const likeCountQuery = await tables.IrregularityLike.count({
    where: {
      irregularity_id: irregularity_id,
      is_liked: true,
      is_deleted: false,
    },
  });
  const commentCountQuery = await tables.IrregularityComment.count({
    where: {
      irregularity_id: irregularity_id,
      is_deleted: false,
    },
  });
  const isLikeQuery = await tables.IrregularityLike.findOne({
    where: { irregularity_id, user_id: id },
  });

  let data = await tables.IrregularityComment.findAll({
    where: { irregularity_id },
    attributes: [
      "id",
      "user_id",
      "irregularity_id",
      "created_at",
      "comment",
      [
        sequelize.literal(`
          (SELECT CASE WHEN EXISTS (
            SELECT is_deleted
            FROM follow
            WHERE follower_id = ${id} AND following_id = irregularity_comment.user_id AND is_deleted = false
          ) THEN 1 ELSE 0 END)
        `),
        "is_following",
      ],
      [
        sequelize.literal(`
            (SELECT CASE WHEN irregularity_comment.user_id = ${id} THEN 1 ELSE 0 END)
          `),
        "is_current_user",
      ],
    ],
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: {
      like_count: likeCountQuery,
      comment_count: commentCountQuery,
      is_like: isLikeQuery ? isLikeQuery.is_liked : false,
      data,
    },
  });
});

exports.addIrregularityLike = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { irregularity_id, is_liked } = req.body;
  let data = await tables.IrregularityLike.findOne({
    where: { user_id: id, irregularity_id },
  });
  if (!data) {
    await tables.IrregularityLike.create({
      user_id: id,
      irregularity_id,
      is_liked,
    });
  } else {
    await tables.IrregularityLike.update(
      { is_liked },
      { where: { user_id: id, irregularity_id } }
    );
  }
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});

exports.getIrregularityById = asyncHandler(async (req, res) => {
  let { irregularity_id } = req.params;
  let data = await tables.Irregularity.findOne({
    where: { id: irregularity_id },
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.editIrregularity = asyncHandler(async (req, res) => {
  let { irregularity_id } = req.params;
  await tables.Irregularity.update(req.body, {
    where: { id: irregularity_id },
  });
  let data = await tables.Irregularity.findOne({
    where: { id: irregularity_id },
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.deleteIrregularity = asyncHandler(async (req, res) => {
  let { irregularity_id } = req.params;
  await tables.Irregularity.destroy({
    where: { id: irregularity_id },
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});
