const { asyncHandler } = require("../utils/catchError");
const AppError = require("../utils/appError");
const uploadImage = require("../utils/upload");
const { tables } = require("../db/index");

exports.uploadMedia = asyncHandler(async (req, res) => {
  let file = req.files.media;
  let url = await uploadImage(file);
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    url,
  });
});

exports.multipleUploadMedia = asyncHandler(async (req, res) => {
  let files = req.files.media;
  let urls;
  if (files.length > 0) {
    const uploadPromises = files.map((file) => uploadImage(file));
    urls = await Promise.all(uploadPromises);
  }
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    urls,
  });
});

exports.getStateList = asyncHandler(async (req, res) => {
  let data = await tables.State.findAll({});
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.getDistrictList = asyncHandler(async (req, res) => {
  const { state_id } = req.query;
  let where = {};
  if (state_id) {
    where.state_id = state_id;
  }
  let data = await tables.District.findAll({
    where,
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.getTalukaList = asyncHandler(async (req, res) => {
  const { state_id, district_id } = req.query;

  let where = {};
  if (state_id) {
    where.state_id = state_id;
  }
  if (district_id) {
    where.district_id = district_id;
  }
  let data = await tables.Taluka.findAll({
    where,
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});
