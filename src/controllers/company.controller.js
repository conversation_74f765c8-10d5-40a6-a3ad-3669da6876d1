let { checkValidation } = require("../validation");
const { tables } = require("../db/index");
const { asyncHandler } = require("../utils/catchError");
const AppError = require("../utils/appError");
const company = require("../db/models/company");
const { Op } = require("sequelize");

exports.registerCompany = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let existingCompany = await tables.User.findOne({
    where: { email: req.body.email },
  });

  if (existingCompany && existingCompany.is_email_verified === true) {
    throw new AppError("email already in use!", 400);
  }

  if (existingCompany) {
    await tables.Company.update(req.body, {
      where: { user_id: existingCompany.id },
    });
    await tables.User.update(req.body, {
      where: { id: existingCompany.id },
    });
  } else {
    const user = await tables.User.create(req.body);
    req.body.user_id = user.id;
    await tables.Company.create(req.body);
  }

  return res.send({
    status: true,
    statusCode: 200,
    message: "company created successfully.",
  });
});

exports.checkAdminApproval = asyncHandler(async (req, res) => {
  let { id } = req.user;
  const company = await tables.Company.findOne({
    where: { user_id: id, is_admin_approved: true },
  });
  let is_admin_approved = company ? company?.is_admin_approved : false;
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: { is_admin_approved },
  });
});
exports.approveCompanyByAdmin = asyncHandler(async (req, res) => {
  let { id } = req.user;
  const company = await tables.Company.update(
    { is_admin_approved: true },
    {
      where: { user_id: id },
    }
  );
  console.log(2, id);
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: { company },
  });
});
exports.getCompanyList = asyncHandler(async (req, res) => {
  let { id } = req.user;
  const {
    query: { searchText },
  } = req;
  let where = { user_type: "COMPANY", id: { [Op.ne]: id } };
  if (searchText) {
    where.name = {
      [Op.iLike]: `%${searchText}%`,
    };
  }
  const data = await tables.User.findAll({
    where,
    attributes: ["id", "name", "user_type", "profile_link"],
    // include: { model: tables.Company },
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: data,
  });
});

exports.addCompanyExpenses = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  const {
    body: {
      company_meet,
      company_expense,
      company_expense_details,
      company_id,
      expense_date,
    },
    user: { id },
  } = req;
  const CompanyMeetArray = [];
  const CompanyExpensesArray = [];
  for (let i = 0; i < company_meet.length; i++) {
    CompanyMeetArray.push({
      meet_details: company_meet[i].meet_details,
      user_id: id,
      company_id: company_id,
      expense_date,
    });
  }
  for (let i = 0; i < company_expense.length; i++) {
    CompanyExpensesArray.push({
      expense_name: company_expense[i].expense_name,
      amount: company_expense[i].amount,
      user_id: id,
      company_id: company_id,
      expense_date,
    });
  }
  company_expense_details.user_id = id;
  company_expense_details.company_id = company_id;
  company_expense_details.expense_date = expense_date;
  await tables.CompanyExpensesDetails.create(company_expense_details);
  await tables.CompanyExpense.bulkCreate(CompanyExpensesArray);
  await tables.CompanyMeet.bulkCreate(CompanyMeetArray);

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});

exports.getCompanyExpensesDetails = asyncHandler(async (req, res) => {
  const {
    params: { company_id, expense_date },
    user: { id },
  } = req;

  const company_expense_details = await tables.CompanyExpensesDetails.findOne({
    where: { company_id, expense_date, user_id: id },
  });
  const company_expense = await tables.CompanyExpense.findAll({
    where: { company_id, expense_date, user_id: id },
  });
  const company_meet = await tables.CompanyMeet.findAll({
    where: { company_id, expense_date, user_id: id },
  });
  const staff_location = await tables.StaffLocation.findAll({
    where: { company_id, expense_date, user_id: id },
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: {
      company_expense_details,
      company_expense,
      company_meet,
      staff_location,
    },
  });
});
