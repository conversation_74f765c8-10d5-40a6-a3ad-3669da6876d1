let { checkValidation } = require("../validation");
const { tables, sequelize } = require("../db/index");
const { asyncHandler } = require("../utils/catchError");
const AppError = require("../utils/appError");

exports.addBuyerTool = asyncHandler(async (req, res) => {
  const checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  req.body.user_id = id;
  let { product_photo } = req.body;
  if (product_photo && product_photo?.length > 3) {
    throw new AppError("only 3 images allowed!", 400);
  }
  let data = await tables.BuyerTool.create(req.body);
  let mediaArray = [];
  for (let i = 0; i < product_photo?.length; i++) {
    mediaArray.push({
      media_link: product_photo[i].media_link,
      user_id: id,
      buyer_requirement_id: data.id,
      media_type: product_photo[i].media_type,
    });
  }
  await tables.BuyerRequirementMedia.bulkCreate(mediaArray);

  return res.send({
    status: true,
    statusCode: 200,
    message: "success.",
  });
});

exports.buyerRequirementsList = asyncHandler(async (req, res) => {
  let { id } = req.user;
  const { is_active, user_id } = req.query;
  let followingData = await tables.Follow.findAll({
    where: { follower_id: id, is_deleted: false },
  });
  const followingIds = followingData.map((follow) => follow.following_id);
  followingIds.push(id);
  let where = { user_id: followingIds, is_deleted: false };
  if (is_active) {
    where.is_active = is_active;
  }
  if (user_id) {
    where.user_id = user_id;
  }

  const data = await tables.BuyerTool.findAll({
    attributes: [
      "id",
      "field",
      "product_name",
      "category",
      "description",
      "created_at",
      "is_active",
      "user_id",
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM buyer_requirement_like WHERE buyer_requirement_id = buyer_requirement.id AND buyer_requirement_like.is_liked = true AND buyer_requirement_like.is_deleted = false)"
        ),
        "like_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM buyer_requirement_comment WHERE buyer_requirement_id = buyer_requirement.id AND buyer_requirement_comment.is_deleted = false)"
        ),
        "comment_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM buyer_requirement_call WHERE buyer_requirement_id = buyer_requirement.id AND buyer_requirement_call.is_deleted = false)"
        ),
        "call_count",
      ],
      [
        sequelize.literal(`
          (SELECT CASE WHEN EXISTS (
            SELECT is_liked
            FROM buyer_requirement_like
            WHERE user_id = ${id} AND buyer_requirement_id = buyer_requirement.id
          ) THEN 
            CASE WHEN (
              SELECT is_liked
              FROM buyer_requirement_like
              WHERE user_id = ${id} AND buyer_requirement_id = buyer_requirement.id
            ) = true THEN 1 ELSE 0 END
          ELSE 0 END)
        `),
        "is_like",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN EXISTS (
          SELECT is_deleted
          FROM follow
          WHERE follower_id = ${id} AND following_id = buyer_requirement.user_id AND is_deleted = false
        ) THEN 1 ELSE 0 END)
      `),
        "is_following",
      ],
      [
        sequelize.literal(`
          (SELECT CASE WHEN buyer_requirement.user_id = ${id} THEN 1 ELSE 0 END)
        `),
        "is_current_user",
      ],
    ],
    include: [
      {
        model: tables.User,
        attributes: [
          "name",
          "email",
          "profile_link",
          [
            sequelize.literal(`(
              SELECT address
              FROM dealer_distributor
              WHERE user_id = "user".id AND is_deleted = false
            )`),
            "address",
          ],
          [
            sequelize.literal(`(
              SELECT mobile
              FROM dealer_distributor
              WHERE user_id = "user".id AND is_deleted = false
            )`),
            "mobile",
          ],
        ],
      },
      {
        model: tables.BuyerRequirementMedia,
        attributes: ["media_type", "media_link"],
      },
    ],
    where,
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success.",
    data,
  });
});

exports.addBuyerRequirementLike = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { buyer_requirement_id, is_liked } = req.body;
  let data = await tables.BuyerRequirementLike.findOne({
    where: { user_id: id, buyer_requirement_id },
  });
  if (!data) {
    await tables.BuyerRequirementLike.create({
      user_id: id,
      buyer_requirement_id,
      is_liked,
    });
  } else {
    await tables.BuyerRequirementLike.update(
      { is_liked },
      { where: { user_id: id, buyer_requirement_id } }
    );
  }
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});

exports.addBuyerRequirementCall = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { buyer_requirement_id } = req.body;

  await tables.BuyerRequirementCall.create({
    user_id: id,
    buyer_requirement_id,
    is_called: true,
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});

exports.getBuyerFieldList = asyncHandler(async (req, res) => {
  const data = await tables.BuyerField.findAll({
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success.",
    data,
  });
});

exports.addBuyerRequirementComment = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { buyer_requirement_id, comment } = req.body;

  let buyerRequirementData = await tables.BuyerRequirementComment.create({
    user_id: id,
    buyer_requirement_id,
    comment,
  });
  let data = await tables.BuyerRequirementComment.findOne({
    where: { id: buyerRequirementData?.id },
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.getBuyerRequirementLikeList = asyncHandler(async (req, res) => {
  let { buyer_requirement_id } = req.params;
  let { id } = req.user;
  const likeCountQuery = await tables.BuyerRequirementLike.count({
    where: {
      buyer_requirement_id: buyer_requirement_id,
      is_liked: true,
      is_deleted: false,
    },
  });
  const commentCountQuery = await tables.BuyerRequirementComment.count({
    where: {
      buyer_requirement_id: buyer_requirement_id,
      is_deleted: false,
    },
  });
  const isLikeQuery = await tables.BuyerRequirementLike.findOne({
    where: { buyer_requirement_id, user_id: id },
  });
  let data = await tables.BuyerRequirementLike.findAll({
    where: { buyer_requirement_id, is_liked: true },
    attributes: [
      "id",
      "user_id",
      "buyer_requirement_id",
      "created_at",
      "is_liked",
      [
        sequelize.literal(`
        (SELECT CASE WHEN EXISTS (
          SELECT is_deleted
          FROM follow
          WHERE follower_id = ${id} AND following_id = buyer_requirement_like.user_id AND is_deleted = false
        ) THEN 1 ELSE 0 END)
      `),
        "is_following",
      ],
      [
        sequelize.literal(`
          (SELECT CASE WHEN buyer_requirement_like.user_id = ${id} THEN 1 ELSE 0 END)
        `),
        "is_current_user",
      ],
    ],
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: {
      like_count: likeCountQuery,
      comment_count: commentCountQuery,
      is_like: isLikeQuery ? isLikeQuery.is_liked : false,
      data,
    },
  });
});

exports.getBuyerRequirementCommentList = asyncHandler(async (req, res) => {
  let { buyer_requirement_id } = req.params;
  let { id } = req.user;
  const likeCountQuery = await tables.BuyerRequirementLike.count({
    where: {
      buyer_requirement_id: buyer_requirement_id,
      is_liked: true,
      is_deleted: false,
    },
  });
  const commentCountQuery = await tables.BuyerRequirementComment.count({
    where: {
      buyer_requirement_id: buyer_requirement_id,
      is_deleted: false,
    },
  });
  const isLikeQuery = await tables.BuyerRequirementLike.findOne({
    where: { buyer_requirement_id, user_id: id },
  });

  let data = await tables.BuyerRequirementComment.findAll({
    where: { buyer_requirement_id },
    attributes: [
      "id",
      "user_id",
      "buyer_requirement_id",
      "created_at",
      "comment",
      [
        sequelize.literal(`
        (SELECT CASE WHEN EXISTS (
          SELECT is_deleted
          FROM follow
          WHERE follower_id = ${id} AND following_id = buyer_requirement_comment.user_id AND is_deleted = false
        ) THEN 1 ELSE 0 END)
      `),
        "is_following",
      ],
      [
        sequelize.literal(`
          (SELECT CASE WHEN buyer_requirement_comment.user_id = ${id} THEN 1 ELSE 0 END)
        `),
        "is_current_user",
      ],
    ],
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: {
      like_count: likeCountQuery,
      comment_count: commentCountQuery,
      is_like: isLikeQuery ? isLikeQuery.is_liked : false,
      data,
    },
  });
});
exports.activeInactiveBuyerTool = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { buyer_requirement_id } = req.params;
  let { is_active } = req.body;

  await tables.BuyerTool.update(
    { is_active },
    { where: { id: buyer_requirement_id } }
  );
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});

exports.deleteBuyerTool = asyncHandler(async (req, res) => {
  let { buyer_requirement_id } = req.params;

  await tables.BuyerTool.update(
    { is_deleted: true },
    { where: { id: buyer_requirement_id } }
  );
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});
