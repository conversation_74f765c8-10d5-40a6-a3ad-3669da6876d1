let { checkValidation } = require("../validation");
const { tables, sequelize } = require("../db/index");
const { asyncHandler } = require("../utils/catchError");
const AppError = require("../utils/appError");

exports.getPostList = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let followingData = await tables.Follow.findAll({
    where: { follower_id: id, is_deleted: false },
  });

  const followingIds = followingData.map((follow) => follow.following_id);
  followingIds.push(id);
  let data = await tables.CompanyPost.findAll({
    attributes: [
      "id",
      "details",
      "created_at",
      "user_id",
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM company_post_like WHERE company_post_id = company_post.id AND company_post_like.is_liked = true AND company_post_like.is_deleted = false)"
        ),
        "like_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM company_post_comment WHERE company_post_id = company_post.id AND company_post_comment.is_deleted = false)"
        ),
        "comment_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM company_post_share WHERE company_post_id = company_post.id AND company_post_share.is_deleted = false)"
        ),
        "share_count",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN EXISTS (
          SELECT is_liked
          FROM company_post_like
          WHERE user_id = ${id} AND company_post_id = company_post.id
        ) THEN 
          CASE WHEN (
            SELECT is_liked
            FROM company_post_like
            WHERE user_id = ${id} AND company_post_id = company_post.id
          ) = true THEN 1 ELSE 0 END
        ELSE 0 END)
      `),
        "is_like",
      ],
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = company_post.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN company_post.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    where: { user_id: followingIds, is_deleted: false },
    include: [
      { model: tables.User, attributes: ["name", "email", "profile_link"] },
      {
        model: tables.CompanyPostMedia,
        attributes: ["media_type", "media_link"],
      },
    ],
    order: [["id", "desc"]],
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});
exports.getPostDetailById = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let { company_post_id } = req.params;
  let data = await tables.CompanyPost.findOne({
    attributes: [
      "id",
      "details",
      "created_at",
      "user_id",
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM company_post_like WHERE company_post_id = company_post.id AND company_post_like.is_liked = true AND company_post_like.is_deleted = false)"
        ),
        "like_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM company_post_comment WHERE company_post_id = company_post.id AND company_post_comment.is_deleted = false)"
        ),
        "comment_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM company_post_share WHERE company_post_id = company_post.id AND company_post_share.is_deleted = false)"
        ),
        "share_count",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN EXISTS (
          SELECT is_liked
          FROM company_post_like
          WHERE user_id = ${id} AND company_post_id = company_post.id
        ) THEN 
          CASE WHEN (
            SELECT is_liked
            FROM company_post_like
            WHERE user_id = ${id} AND company_post_id = company_post.id
          ) = true THEN 1 ELSE 0 END
        ELSE 0 END)
      `),
        "is_like",
      ],
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = company_post.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN company_post.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    where: { id: company_post_id, is_deleted: false },
    include: [
      { model: tables.User, attributes: ["name", "email", "profile_link"] },
      {
        model: tables.CompanyPostMedia,
        attributes: ["media_type", "media_link"],
      },
    ],
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});
exports.addPost = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  req.body.user_id = id;
  let { post_media } = req.body;
  let postData = await tables.CompanyPost.create(req.body);
  let postMediaArray = [];
  for (let i = 0; i < post_media.length; i++) {
    postMediaArray.push({
      media_link: post_media[i].media_link,
      user_id: id,
      company_post_id: postData.id,
      media_type: post_media[i].media_type,
    });
  }
  await tables.CompanyPostMedia.bulkCreate(postMediaArray);
  return res.send({
    status: true,
    statusCode: 200,
    message: "company_post uploaded",
  });
});

exports.addPostLike = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { company_post_id, is_liked } = req.body;
  let data = await tables.CompanyPostLike.findOne({
    where: { user_id: id, company_post_id },
  });
  if (!data) {
    await tables.CompanyPostLike.create({
      user_id: id,
      company_post_id,
      is_liked,
    });
  } else {
    await tables.CompanyPostLike.update(
      { is_liked },
      { where: { user_id: id, company_post_id } }
    );
  }
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});
exports.addPostComment = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { company_post_id, comment } = req.body;

  let postData = await tables.CompanyPostComment.create({
    user_id: id,
    company_post_id,
    comment,
  });
  let data = await tables.CompanyPostComment.findOne({
    where: { id: postData?.id },
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.addPostShare = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { company_post_id } = req.body;

  await tables.CompanyPostShare.create({
    user_id: id,
    company_post_id,
    is_shared: true,
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});

exports.getPostLikeList = asyncHandler(async (req, res) => {
  let { company_post_id } = req.params;
  let { id } = req.user;
  const likeCountQuery = await tables.CompanyPostLike.count({
    where: {
      company_post_id: company_post_id,
      is_liked: true,
      is_deleted: false,
    },
  });
  const commentCountQuery = await tables.CompanyPostComment.count({
    where: {
      company_post_id: company_post_id,
      is_deleted: false,
    },
  });
  const isLikeQuery = await tables.CompanyPostLike.findOne({
    where: { company_post_id, user_id: id },
  });
  let data = await tables.CompanyPostLike.findAll({
    where: { company_post_id, is_liked: true },
    attributes: [
      "id",
      "user_id",
      "company_post_id",
      "created_at",
      "is_liked",
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = company_post_like.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN company_post_like.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: {
      like_count: likeCountQuery,
      comment_count: commentCountQuery,
      is_like: isLikeQuery ? isLikeQuery.is_liked : false,
      data,
    },
  });
});

exports.getPostCommentList = asyncHandler(async (req, res) => {
  let { company_post_id } = req.params;
  let { id } = req.user;
  const likeCountQuery = await tables.CompanyPostLike.count({
    where: {
      company_post_id: company_post_id,
      is_liked: true,
      is_deleted: false,
    },
  });
  const commentCountQuery = await tables.CompanyPostComment.count({
    where: {
      company_post_id: company_post_id,
      is_deleted: false,
    },
  });
  const isLikeQuery = await tables.CompanyPostLike.findOne({
    where: { company_post_id, user_id: id },
  });

  let data = await tables.CompanyPostComment.findAll({
    where: { company_post_id },
    attributes: [
      "id",
      "user_id",
      "company_post_id",
      "created_at",
      "comment",
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = company_post_comment.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN company_post_comment.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: {
      like_count: likeCountQuery,
      comment_count: commentCountQuery,
      is_like: isLikeQuery ? isLikeQuery.is_liked : false,
      data,
    },
  });
});
