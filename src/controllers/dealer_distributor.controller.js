let { checkValidation } = require("../validation");
const { tables } = require("../db/index");
const { asyncHandler } = require("../utils/catchError");
const AppError = require("../utils/appError");

exports.registerDealerDistributor = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let existingUser = await tables.User.findOne({
    where: { email: req.body.email },
  });

  if (existingUser && existingUser.is_email_verified === true) {
    throw new AppError("email already in use!", 400);
  }

  if (existingUser) {
    await tables.DealerDistributor.update(req.body, {
      where: { user_id: existingUser.id },
    });
    await tables.User.update(req.body, {
      where: { id: existingUser.id },
    });
  } else {
    const user = await tables.User.create(req.body);
    req.body.user_id = user.id;
    await tables.DealerDistributor.create(req.body);
  }

  return res.send({
    status: true,
    statusCode: 200,
    message: "dealer/distributor created successfully.",
  });
});
