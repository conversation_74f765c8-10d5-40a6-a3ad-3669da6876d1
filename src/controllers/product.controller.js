let { checkValidation } = require("../validation");
const { tables, sequelize } = require("../db/index");
const { asyncHandler } = require("../utils/catchError");
const AppError = require("../utils/appError");

exports.getProductList = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let followingData = await tables.Follow.findAll({
    where: { follower_id: id, is_deleted: false },
  });

  const followingIds = followingData.map((follow) => follow.following_id);
  followingIds.push(id);
  let data = await tables.Product.findAll({
    attributes: [
      "id",
      "product_name",
      "description",
      "created_at",
      "user_id",
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM product_like WHERE product_id = product.id AND product_like.is_liked = true AND product_like.is_deleted = false)"
        ),
        "like_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM product_comment WHERE product_id = product.id AND product_comment.is_deleted = false)"
        ),
        "comment_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM product_share WHERE product_id = product.id AND product_share.is_deleted = false)"
        ),
        "share_count",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN EXISTS (
          SELECT is_liked
          FROM product_like
          WHERE user_id = ${id} AND product_id = product.id
        ) THEN 
          CASE WHEN (
            SELECT is_liked
            FROM product_like
            WHERE user_id = ${id} AND product_id = product.id
          ) = true THEN 1 ELSE 0 END
        ELSE 0 END)
      `),
        "is_like",
      ],
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = product.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN product.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    where: { user_id: followingIds, is_deleted: false },
    include: [
      { model: tables.User, attributes: ["name", "email", "profile_link"] },
      {
        model: tables.ProductMedia,
        where: { is_deleted: false },
        attributes: ["id", "media_type", "media_link"],
      },
    ],
    order: [["id", "desc"]],
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});
exports.getProductDetailById = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let { product_id } = req.params;
  let data = await tables.Product.findOne({
    attributes: [
      "id",
      "product_name",
      "description",
      "created_at",
      "user_id",
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM product_like WHERE product_id = product.id AND product_like.is_liked = true AND product_like.is_deleted = false)"
        ),
        "like_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM product_comment WHERE product_id = product.id AND product_comment.is_deleted = false)"
        ),
        "comment_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM product_share WHERE product_id = product.id AND product_share.is_deleted = false)"
        ),
        "share_count",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN EXISTS (
          SELECT is_liked
          FROM product_like
          WHERE user_id = ${id} AND product_id = product.id
        ) THEN 
          CASE WHEN (
            SELECT is_liked
            FROM product_like
            WHERE user_id = ${id} AND product_id = product.id
          ) = true THEN 1 ELSE 0 END
        ELSE 0 END)
      `),
        "is_like",
      ],
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = product.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN product.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    where: { id: product_id, is_deleted: false },
    include: [
      { model: tables.User, attributes: ["name", "email", "profile_link"] },
      {
        model: tables.ProductMedia,
        where: { is_deleted: false },

        attributes: ["id", "media_type", "media_link"],
      },
    ],
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});
exports.addProduct = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  req.body.user_id = id;
  let { product_media } = req.body;
  let productData = await tables.Product.create(req.body);
  let productMediaArray = [];
  for (let i = 0; i < product_media.length; i++) {
    productMediaArray.push({
      media_link: product_media[i].media_link,
      user_id: id,
      product_id: productData.id,
      media_type: product_media[i].media_type,
    });
  }
  await tables.ProductMedia.bulkCreate(productMediaArray);
  return res.send({
    status: true,
    statusCode: 200,
    message: "product uploaded",
  });
});

exports.addProductLike = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { product_id, is_liked } = req.body;
  let data = await tables.ProductLike.findOne({
    where: { user_id: id, product_id },
  });
  if (!data) {
    await tables.ProductLike.create({ user_id: id, product_id, is_liked });
  } else {
    await tables.ProductLike.update(
      { is_liked },
      { where: { user_id: id, product_id } }
    );
  }
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});
exports.addProductComment = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { product_id, comment } = req.body;

  let productData = await tables.ProductComment.create({
    user_id: id,
    product_id,
    comment,
  });
  let data = await tables.ProductComment.findOne({
    where: { id: productData?.id },
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.addProductShare = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { product_id } = req.body;

  await tables.ProductShare.create({
    user_id: id,
    product_id,
    is_shared: true,
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});

exports.getProductLikeList = asyncHandler(async (req, res) => {
  let { product_id } = req.params;
  let { id } = req.user;
  const likeCountQuery = await tables.ProductLike.count({
    where: {
      product_id: product_id,
      is_liked: true,
      is_deleted: false,
    },
  });
  const commentCountQuery = await tables.ProductComment.count({
    where: {
      product_id: product_id,
      is_deleted: false,
    },
  });
  const isLikeQuery = await tables.ProductLike.findOne({
    where: { product_id, user_id: id },
  });
  let data = await tables.ProductLike.findAll({
    where: { product_id, is_liked: true },
    attributes: [
      "id",
      "user_id",
      "product_id",
      "created_at",
      "is_liked",
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = product_like.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN product_like.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: {
      like_count: likeCountQuery,
      comment_count: commentCountQuery,
      is_like: isLikeQuery ? isLikeQuery.is_liked : false,
      data,
    },
  });
});

exports.getProductCommentList = asyncHandler(async (req, res) => {
  let { product_id } = req.params;
  let { id } = req.user;
  const likeCountQuery = await tables.ProductLike.count({
    where: {
      product_id: product_id,
      is_liked: true,
      is_deleted: false,
    },
  });
  const commentCountQuery = await tables.ProductComment.count({
    where: {
      product_id: product_id,
      is_deleted: false,
    },
  });
  const isLikeQuery = await tables.ProductLike.findOne({
    where: { product_id, user_id: id },
  });

  let data = await tables.ProductComment.findAll({
    where: { product_id },
    attributes: [
      "id",
      "user_id",
      "product_id",
      "created_at",
      "comment",
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = product_comment.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN product_comment.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: {
      like_count: likeCountQuery,
      comment_count: commentCountQuery,
      is_like: isLikeQuery ? isLikeQuery.is_liked : false,
      data,
    },
  });
});

exports.editProduct = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let { product_id } = req.params;
  let { product_media, product_media_delete_ids } = req.body;
  await tables.Product.update(req.body, {
    where: { id: product_id },
  });

  if (product_media_delete_ids) {
    await tables.ProductMedia.update(
      { is_deleted: true },
      {
        where: { id: product_media_delete_ids },
      }
    );
  }
  if (product_media) {
    let postMediaArray = [];
    for (let i = 0; i < product_media.length; i++) {
      postMediaArray.push({
        media_link: product_media[i].media_link,
        user_id: id,
        product_id,
        media_type: product_media[i].media_type,
      });
    }
    await tables.ProductMedia.bulkCreate(postMediaArray);
  }
  return res.send({
    status: true,
    statusCode: 200,
    message: "product updated",
  });
});

exports.deleteProduct = asyncHandler(async (req, res) => {
  let { product_id } = req.params;

  await tables.Product.update(
    { is_deleted: true },
    { where: { id: product_id } }
  );
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});
