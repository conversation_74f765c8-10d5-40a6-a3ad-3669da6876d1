const { tables, sequelize } = require("../db/index");
const { asyncHandler } = require("../utils/catchError");
let { checkValidation } = require("../validation");
const AppError = require("../utils/appError");

exports.getSubscriptionList = asyncHandler(async (req, res) => {
  let data = await tables.Subscription.findAll({
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.addSubscription = asyncHandler(async (req, res) => {
  let data = await tables.Subscription.findAll({
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.addUserSubscription = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { subscription_id } = req.body;
  let checkSub = await tables.UserSubscription.findOne({
    where: { user_id: id, subscription_id },
  });
  if (checkSub) {
    throw new AppError("already subscribed to another package!", 400);
  }
  await tables.UserSubscription.create({
    user_id: id,
    subscription_id,
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});

exports.userSubscriptionDetails = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let data = await tables.UserSubscription.findOne({
    where: { user_id: id },
    include: { model: tables.Subscription },
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});
