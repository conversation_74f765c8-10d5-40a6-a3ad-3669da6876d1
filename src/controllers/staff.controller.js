let { checkValidation } = require("../validation");
const { tables, sequelize, Sequelize } = require("../db/index");
const { asyncHandler } = require("../utils/catchError");
const AppError = require("../utils/appError");
const { Op } = require("sequelize");

exports.registerStaff = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let existingStaff = await tables.User.findOne({
    where: { email: req.body.email },
  });

  if (existingStaff && existingStaff.is_email_verified === true) {
    throw new AppError("email already in use!", 400);
  }

  if (existingStaff) {
    await tables.Staff.update(req.body, {
      where: { user_id: existingStaff.id },
    });
    await tables.User.update(req.body, {
      where: { id: existingStaff.id },
    });
  } else {
    const user = await tables.User.create(req.body);
    req.body.user_id = user.id;
    await tables.Staff.create(req.body);
  }

  return res.send({
    status: true,
    statusCode: 200,
    message: "staff created successfully.",
  });
});

exports.membersList = asyncHandler(async (req, res) => {
  const {
    query: { searchText },
  } = req;
  let where = {
    user_type: "STAFF",
  };
  if (searchText) {
    where.name = {
      [Op.iLike]: `%${searchText}%`,
    };
  }
  let { id } = req.user;

  const result = await tables.User.findAll({
    where,
    attributes: [
      "id",
      "name",
      "email",
      "password",
      "user_type",
      "profile_link",
      [
        sequelize.literal(`
          (SELECT 
            CASE 
              WHEN EXISTS (
                SELECT id
                FROM team_member
                WHERE user_id = ${id} AND team_member.member_id = "user".id AND is_del = false
              ) THEN 1 
              ELSE 0 
            END
          )`),
        "is_member",
      ],
    ],
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "members found.",
    data: result,
  });
});

exports.addStaffLocation = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let { latitude, longitude } = req.body;

  await tables.StaffLocation.create({
    latitude,
    longitude,
    user_id: id,
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "location added.",
  });
});

exports.staffLocationList = asyncHandler(async (req, res) => {
  let id = req.query?.user_id;
  let where = { user_id: id };
  if (req.query?.from && req.query?.to) {
    where.created_at = {
      [Sequelize.Op.between]: [req.query?.from, req.query?.to],
    };
  }

  const data = await tables.StaffLocation.findAll({ where });
  return res.send({
    status: true,
    statusCode: 200,
    message: "location list.",
    data,
  });
});

exports.getStaffCompanyDetail = asyncHandler(async (req, res) => {
  const staffId = req.params.staff_id;
  let data = await tables.StaffCompany.findOne({
    where: { user_id: staffId, is_company_added: true },
  });

  if (!data) {
    data = { is_company_added: false };
  }
  return res.send({
    status: true,
    statusCode: 200,
    message: "staff company details.",
    data,
  });
});

exports.addStaffCompany = asyncHandler(async (req, res) => {
  const { staff_id, company_id } = req.body;
  let data = await tables.StaffCompany.findOne({
    where: { user_id: staff_id, is_company_added: true },
  });

  if (data) {
    throw new AppError("staff has already added company!", 400);
  }
  let checkdata = await tables.StaffCompany.findOne({
    where: { user_id: staff_id, company_id },
  });

  if (checkdata) {
    checkdata.is_company_added = true;
    checkdata.save();
  } else {
    await tables.StaffCompany.create({
      user_id: staff_id,
      company_id,
      is_company_added: true,
    });
  }

  return res.send({
    status: true,
    statusCode: 200,
    message: "staff company added.",
  });
});

exports.removeStaffCompany = asyncHandler(async (req, res) => {
  const { staff_id } = req.params;
  let data = await tables.StaffCompany.findOne({
    where: { user_id: staff_id, is_company_added: true },
  });
  if (!data) {
    throw new AppError("staff doesn't have company!", 400);
  }
  if (data) {
    data.is_company_added = false;
    data.save();
  }
  return res.send({
    status: true,
    statusCode: 200,
    message: "staff company removed.",
    data,
  });
});

exports.approveStaffCompany = asyncHandler(async (req, res) => {
  const { staff_id, company_id } = req.params;
  let data = await tables.StaffCompany.findOne({
    where: { user_id: staff_id, company_id, is_company_added: true },
  });
  if (!data) {
    throw new AppError("staff company not exist!", 400);
  }
  data.is_admin_approved = true;
  data.save();
  return res.send({
    status: true,
    statusCode: 200,
    message: "staff company approved.",
    data,
  });
});

exports.staffProfile = asyncHandler(async (req, res) => {
  const { id } = req.user;
  const userDetails = await tables.User.findOne({
    where: { id },
  });
  const staffDetails = await tables.Staff.findOne({
    where: { user_id: id },
  });
  const profileDetails = {
    ...userDetails?.dataValues,
    ...staffDetails?.dataValues,
  };

  return res.send({
    status: true,
    statusCode: 200,
    message: "staff Profile details.",
    data: profileDetails,
  });
});
exports.editStaffProfile = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  const { id } = req.user;

  await tables.Staff.update(req.body, {
    where: { user_id: id },
  });
  await tables.User.update(req.body, {
    where: { id: id },
  });
  const userDetails = await tables.User.findOne({
    where: { id },
  });
  const staffDetails = await tables.Staff.findOne({
    where: { user_id: id },
  });
  const profileDetails = {
    ...userDetails?.dataValues,
    ...staffDetails?.dataValues,
  };
  return res.send({
    status: true,
    statusCode: 200,
    message: "staff Profile updated successfully.",
    data: profileDetails,
  });
});

exports.staffProfileList = asyncHandler(async (req, res) => {
  const { id } = req.user;

  const staffDetails = await tables.Staff.findOne({
    where: { user_id: id },
  });
  const userDetails = await tables.User.findOne({
    where: { id },
  });
  let data = await tables.Post.findAll({
    attributes: [
      "id",
      "details",
      "created_at",
      "user_id",
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM post_like WHERE post_id = post.id AND post_like.is_liked = true AND post_like.is_deleted = false)"
        ),
        "like_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM post_comment WHERE post_id = post.id AND post_comment.is_deleted = false)"
        ),
        "comment_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM post_share WHERE post_id = post.id AND post_share.is_deleted = false)"
        ),
        "share_count",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN EXISTS (
          SELECT is_liked
          FROM post_like
          WHERE user_id = ${id} AND post_id = post.id
        ) THEN 
          CASE WHEN (
            SELECT is_liked
            FROM post_like
            WHERE user_id = ${id} AND post_id = post.id
          ) = true THEN 1 ELSE 0 END
        ELSE 0 END)
      `),
        "is_like",
      ],
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = post.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN post.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    where: { user_id: id, is_deleted: false },
    include: [
      { model: tables.User, attributes: ["name", "email", "profile_link"] },
      { model: tables.PostMedia, attributes: ["media_type", "media_link"] },
    ],
    order: [["id", "desc"]],
  });
  const followerCount = await tables.Follow.count({
    where: {
      following_id: id,
      is_deleted: false,
    },
  });
  const followingCount = await tables.Follow.count({
    where: {
      follower_id: id,
      is_deleted: false,
    },
  });
  let postCount = await tables.Post.count({
    where: { user_id: id, is_deleted: false },
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "staff bio.",
    data: {
      data,
      user: {
        user_name: staffDetails?.user_name,
        name: userDetails?.name,
        bio: staffDetails?.bio,
        cover_image: staffDetails?.cover_image,
        profile_link: userDetails?.profile_link,
        followerCount,
        followingCount,
        postCount,
      },
    },
  });
});
