let { checkValidation } = require("../validation");
const { tables, sequelize } = require("../db/index");
const { asyncHandler } = require("../utils/catchError");
const AppError = require("../utils/appError");

exports.getCompanyLeadPostList = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let followingData = await tables.Follow.findAll({
    where: { follower_id: id, is_deleted: false },
  });

  const followingIds = followingData.map((follow) => follow.following_id);
  followingIds.push(id);
  let data = await tables.CompanyLeadPost.findAll({
    attributes: [
      "id",
      "details",
      "created_at",
      "user_id",
      "company_id",
      "title",
      "product_name",
      "category",
      "location",
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM company_lead_like WHERE company_lead_id = company_lead.id AND company_lead_like.is_liked = true AND company_lead_like.is_deleted = false)"
        ),
        "like_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM company_lead_comment WHERE company_lead_id = company_lead.id AND company_lead_comment.is_deleted = false)"
        ),
        "comment_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM company_lead_share WHERE company_lead_id = company_lead.id AND company_lead_share.is_deleted = false)"
        ),
        "share_count",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN EXISTS (
          SELECT is_liked
          FROM company_lead_like
          WHERE user_id = ${id} AND company_lead_id = company_lead.id
        ) THEN 
          CASE WHEN (
            SELECT is_liked
            FROM company_lead_like
            WHERE user_id = ${id} AND company_lead_id = company_lead.id
          ) = true THEN 1 ELSE 0 END
        ELSE 0 END)
      `),
        "is_like",
      ],
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = company_lead.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN company_lead.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    where: { user_id: followingIds, is_deleted: false },
    include: [
      { model: tables.User, attributes: ["name", "email", "profile_link"] },
      {
        model: tables.CompanyLeadPostMedia,
        attributes: ["media_type", "media_link"],
      },
    ],
    order: [["id", "desc"]],
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.getCompanyLeadPostDetailById = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let { company_lead_id } = req.params;
  let data = await tables.CompanyLeadPost.findOne({
    attributes: [
      "id",
      "details",
      "created_at",
      "user_id",
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM company_lead_like WHERE company_lead_id = company_lead.id AND company_lead_like.is_liked = true AND company_lead_like.is_deleted = false)"
        ),
        "like_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM company_lead_comment WHERE company_lead_id = company_lead.id AND company_lead_comment.is_deleted = false)"
        ),
        "comment_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM company_lead_share WHERE company_lead_id = company_lead.id AND company_lead_share.is_deleted = false)"
        ),
        "share_count",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN EXISTS (
          SELECT is_liked
          FROM company_lead_like
          WHERE user_id = ${id} AND company_lead_id = company_lead.id
        ) THEN 
          CASE WHEN (
            SELECT is_liked
            FROM company_lead_like
            WHERE user_id = ${id} AND company_lead_id = company_lead.id
          ) = true THEN 1 ELSE 0 END
        ELSE 0 END)
      `),
        "is_like",
      ],
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = company_lead.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN company_lead.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    where: { id: company_lead_id, is_deleted: false },
    include: [
      { model: tables.User, attributes: ["name", "email", "profile_link"] },
      {
        model: tables.CompanyLeadPostMedia,
        attributes: ["media_type", "media_link"],
      },
    ],
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.addCompanyLeadPost = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  req.body.user_id = id;
  let { company_lead_post_media } = req.body;
  let postData = await tables.CompanyLeadPost.create(req.body);
  let postMediaArray = [];
  for (let i = 0; i < company_lead_post_media.length; i++) {
    postMediaArray.push({
      media_link: company_lead_post_media[i].media_link,
      user_id: id,
      company_lead_id: postData.id,
      media_type: company_lead_post_media[i].media_type,
    });
  }
  await tables.CompanyLeadPostMedia.bulkCreate(postMediaArray);
  return res.send({
    status: true,
    statusCode: 200,
    message: "company_lead uploaded",
  });
});

exports.addCompanyLeadPostLike = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { company_lead_id, is_liked } = req.body;
  let data = await tables.CompanyLeadPostLike.findOne({
    where: { user_id: id, company_lead_id },
  });
  if (!data) {
    await tables.CompanyLeadPostLike.create({
      user_id: id,
      company_lead_id,
      is_liked,
    });
  } else {
    await tables.CompanyLeadPostLike.update(
      { is_liked },
      { where: { user_id: id, company_lead_id } }
    );
  }
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});

exports.addCompanyLeadPostComment = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { company_lead_id, comment } = req.body;

  let postData = await tables.CompanyLeadPostComment.create({
    user_id: id,
    company_lead_id,
    comment,
  });
  let data = await tables.CompanyLeadPostComment.findOne({
    where: { id: postData?.id },
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.addCompanyLeadPostShare = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { company_lead_id } = req.body;

  await tables.CompanyLeadPostShare.create({
    user_id: id,
    company_lead_id,
    is_shared: true,
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});

exports.getCompanyLeadPostLikeList = asyncHandler(async (req, res) => {
  let { company_lead_id } = req.params;
  let { id } = req.user;
  const likeCountQuery = await tables.CompanyLeadPostLike.count({
    where: {
      company_lead_id: company_lead_id,
      is_liked: true,
      is_deleted: false,
    },
  });
  const commentCountQuery = await tables.CompanyLeadPostComment.count({
    where: {
      company_lead_id: company_lead_id,
      is_deleted: false,
    },
  });
  const isLikeQuery = await tables.CompanyLeadPostLike.findOne({
    where: { company_lead_id, user_id: id },
  });
  let data = await tables.CompanyLeadPostLike.findAll({
    where: { company_lead_id, is_liked: true },
    attributes: [
      "id",
      "user_id",
      "company_lead_id",
      "created_at",
      "is_liked",
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = company_lead_like.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN company_lead_like.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: {
      like_count: likeCountQuery,
      comment_count: commentCountQuery,
      is_like: isLikeQuery ? isLikeQuery.is_liked : false,
      data,
    },
  });
});

exports.getCompanyLeadPostCommentList = asyncHandler(async (req, res) => {
  let { company_lead_id } = req.params;
  let { id } = req.user;
  const likeCountQuery = await tables.CompanyLeadPostLike.count({
    where: {
      company_lead_id: company_lead_id,
      is_liked: true,
      is_deleted: false,
    },
  });
  const commentCountQuery = await tables.CompanyLeadPostComment.count({
    where: {
      company_lead_id: company_lead_id,
      is_deleted: false,
    },
  });
  const isLikeQuery = await tables.CompanyLeadPostLike.findOne({
    where: { company_lead_id, user_id: id },
  });

  let data = await tables.CompanyLeadPostComment.findAll({
    where: { company_lead_id },
    attributes: [
      "id",
      "user_id",
      "company_lead_id",
      "created_at",
      "comment",
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = company_lead_comment.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN company_lead_comment.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: {
      like_count: likeCountQuery,
      comment_count: commentCountQuery,
      is_like: isLikeQuery ? isLikeQuery.is_liked : false,
      data,
    },
  });
});
