let { checkValidation } = require("../validation");
const { tables } = require("../db/index");
const { asyncHandler } = require("../utils/catchError");
const AppError = require("../utils/appError");

exports.addFollowing = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { following_id } = req.body;

  let data = await tables.Follow.findOne({
    where: { following_id, follower_id: id, is_deleted: false },
  });

  if (data) {
    throw new AppError("already following", 400);
  }
  await tables.Follow.create({ following_id, follower_id: id });

  return res.send({
    status: true,
    statusCode: 200,
    message: "followed successfully",
  });
});

exports.unfollowUser = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { following_id } = req.body;

  let data = await tables.Follow.findOne({
    where: { following_id, follower_id: id, is_deleted: false },
  });

  if (!data) {
    throw new AppError("user not following", 400);
  }
  await tables.Follow.destroy({ where: { following_id, follower_id: id } });

  return res.send({
    status: true,
    statusCode: 200,
    message: "unfollowed successfully",
  });
});
