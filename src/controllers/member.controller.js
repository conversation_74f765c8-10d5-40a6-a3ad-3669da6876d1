let { checkValidation } = require("../validation");
const { tables } = require("../db/index");
const { asyncHandler } = require("../utils/catchError");
const AppError = require("../utils/appError");
const { Op } = require("sequelize");

exports.addMember = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { member_id } = req.body;
  let data = await tables.TeamMember.findOne({
    where: { member_id, user_id: id, is_del: false },
  });
  if (data) {
    throw new AppError("user already added!", 400);
  }
  const memberData = await tables.TeamMember.create({
    member_id,
    user_id: id,
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "member added",
    data: memberData,
  });
});

exports.updateMember = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { team_member_id } = req.params;
  let { member_id } = req.body;
  let data = await tables.TeamMember.findOne({
    where: { member_id, user_id: id, is_del: false },
  });
  if (data) {
    throw new AppError("user already added!", 400);
  }
  await tables.TeamMember.update(
    {
      member_id,
    },
    { where: { id: team_member_id } }
  );
  const memberData = await tables.TeamMember.findAll({
    where: { user_id: id, is_del: false },
    include: [{ model: tables.User, as: "member" }],
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "member replaced",
    data: memberData,
  });
});

exports.getTeamMemberList = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let data = await tables.TeamMember.findAll({
    where: { user_id: id, is_del: false },
    include: [{ model: tables.User, as: "member" }],
  });
  let subscription_details = await tables.UserSubscription.findOne({
    where: { user_id: id },
    include: { model: tables.Subscription },
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "member list",
    data: { data, subscription_details },
  });
});

exports.deleteMember = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { team_member_id } = req.params;
  let data = await tables.TeamMember.findOne({
    where: { id: team_member_id, is_del: false },
  });
  if (!data) {
    throw new AppError("member does not exist!", 400);
  }
  await tables.TeamMember.update(
    {
      is_del: true,
    },
    { where: { id: team_member_id } }
  );

  return res.send({
    status: true,
    statusCode: 200,
    message: "member deleted",
  });
});
