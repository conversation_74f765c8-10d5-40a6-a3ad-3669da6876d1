let { checkValidation } = require("../validation");
const { tables, sequelize } = require("../db/index");
const { asyncHandler } = require("../utils/catchError");
const AppError = require("../utils/appError");
const { where } = require("sequelize");

exports.getHiringPostList = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let followingData = await tables.Follow.findAll({
    where: { follower_id: id, is_deleted: false },
  });

  const followingIds = followingData.map((follow) => follow.following_id);
  followingIds.push(id);
  let data = await tables.HiringPost.findAll({
    attributes: [
      "id",
      "position",
      "salary",
      "location",
      "description",
      "created_at",
      "user_id",
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM hiring_post_interest WHERE hiring_post_id = hiring_post.id AND hiring_post_interest.is_interested = true AND hiring_post_interest.is_deleted = false)"
        ),
        "interest_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM hiring_post_comment WHERE hiring_post_id = hiring_post.id AND hiring_post_comment.is_deleted = false)"
        ),
        "comment_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM hiring_post_share WHERE hiring_post_id = hiring_post.id AND hiring_post_share.is_deleted = false)"
        ),
        "share_count",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN EXISTS (
          SELECT is_interested
          FROM hiring_post_interest
          WHERE user_id = ${id} AND hiring_post_id = hiring_post.id
        ) THEN 
          CASE WHEN (
            SELECT is_interested
            FROM hiring_post_interest
            WHERE user_id = ${id} AND hiring_post_id = hiring_post.id
          ) = true THEN 1 ELSE 0 END
        ELSE 0 END)
      `),
        "is_interested",
      ],
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = hiring_post.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN hiring_post.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    where: { user_id: followingIds, is_deleted: false },
    include: [
      {
        model: tables.User,
        attributes: [
          "id",
          "name",
          "email",
          "profile_link",
          ["name", "company_name"],
        ],
      },
      {
        model: tables.HiringPostMedia,
        where: { is_deleted: false },
        attributes: ["id", "media_type", "media_link"],
      },
    ],
    order: [["id", "desc"]],
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});
exports.getHiringPostDetailById = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let { hiring_post_id } = req.params;
  let data = await tables.HiringPost.findOne({
    attributes: [
      "id",
      "position",
      "salary",
      "location",
      "description",
      "created_at",
      "user_id",
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM hiring_post_interest WHERE hiring_post_id = hiring_post.id AND hiring_post_interest.is_interested = true AND hiring_post_interest.is_deleted = false)"
        ),
        "interest_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM hiring_post_comment WHERE hiring_post_id = hiring_post.id AND hiring_post_comment.is_deleted = false)"
        ),
        "comment_count",
      ],
      [
        sequelize.literal(
          "(SELECT COUNT(*) FROM hiring_post_share WHERE hiring_post_id = hiring_post.id AND hiring_post_share.is_deleted = false)"
        ),
        "share_count",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN EXISTS (
          SELECT is_interested
          FROM hiring_post_interest
          WHERE user_id = ${id} AND hiring_post_id = hiring_post.id
        ) THEN 
          CASE WHEN (
            SELECT is_interested
            FROM hiring_post_interest
            WHERE user_id = ${id} AND hiring_post_id = hiring_post.id
          ) = true THEN 1 ELSE 0 END
        ELSE 0 END)
      `),
        "is_interested",
      ],
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = hiring_post.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN hiring_post.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    where: { id: hiring_post_id, is_deleted: false },
    include: [
      { model: tables.User, attributes: ["name", "email", "profile_link"] },
      {
        model: tables.HiringPostMedia,
        where: { is_deleted: false },
        attributes: ["id", "media_type", "media_link"],
      },
    ],
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});
exports.addHiringPost = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  req.body.user_id = id;
  let { post_media } = req.body;
  let postData = await tables.HiringPost.create(req.body);
  let postMediaArray = [];
  for (let i = 0; i < post_media.length; i++) {
    postMediaArray.push({
      media_link: post_media[i].media_link,
      user_id: id,
      hiring_post_id: postData.id,
      media_type: post_media[i].media_type,
    });
  }
  await tables.HiringPostMedia.bulkCreate(postMediaArray);
  return res.send({
    status: true,
    statusCode: 200,
    message: "hiring_post uploaded",
  });
});

exports.addHiringPostLike = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { hiring_post_id, is_interested } = req.body;
  let data = await tables.HiringPostInterest.findOne({
    where: { user_id: id, hiring_post_id },
  });
  if (!data) {
    await tables.HiringPostInterest.create({
      user_id: id,
      hiring_post_id,
      is_interested,
    });
  } else {
    await tables.HiringPostInterest.update(
      { is_interested },
      { where: { user_id: id, hiring_post_id } }
    );
  }
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});
exports.addHiringPostComment = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { hiring_post_id, comment } = req.body;

  let postData = await tables.HiringPostComment.create({
    user_id: id,
    hiring_post_id,
    comment,
  });
  let data = await tables.HiringPostComment.findOne({
    where: { id: postData?.id },
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.addHiringPostShare = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  let { id } = req.user;
  let { hiring_post_id } = req.body;

  await tables.HiringPostShare.create({
    user_id: id,
    hiring_post_id,
    is_shared: true,
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});

exports.getHiringPostLikeList = asyncHandler(async (req, res) => {
  let { hiring_post_id } = req.params;
  let { id } = req.user;
  const likeCountQuery = await tables.HiringPostInterest.count({
    where: {
      hiring_post_id: hiring_post_id,
      is_interested: true,
      is_deleted: false,
    },
  });
  const commentCountQuery = await tables.HiringPostComment.count({
    where: {
      hiring_post_id: hiring_post_id,
      is_deleted: false,
    },
  });
  const isLikeQuery = await tables.HiringPostInterest.findOne({
    where: { hiring_post_id, user_id: id },
  });
  let data = await tables.HiringPostInterest.findAll({
    where: { hiring_post_id, is_interested: true },
    attributes: [
      "id",
      "user_id",
      "hiring_post_id",
      "created_at",
      "is_interested",
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = hiring_post_interest.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN hiring_post_interest.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: {
      interest_count: likeCountQuery,
      comment_count: commentCountQuery,
      is_interested: isLikeQuery ? isLikeQuery.is_interested : false,
      data,
    },
  });
});

exports.getHiringPostCommentList = asyncHandler(async (req, res) => {
  let { hiring_post_id } = req.params;
  let { id } = req.user;
  const likeCountQuery = await tables.HiringPostInterest.count({
    where: {
      hiring_post_id: hiring_post_id,
      is_interested: true,
      is_deleted: false,
    },
  });
  const commentCountQuery = await tables.HiringPostComment.count({
    where: {
      hiring_post_id: hiring_post_id,
      is_deleted: false,
    },
  });
  const isLikeQuery = await tables.HiringPostInterest.findOne({
    where: { hiring_post_id, user_id: id },
  });

  let data = await tables.HiringPostComment.findAll({
    where: { hiring_post_id },
    attributes: [
      "id",
      "user_id",
      "hiring_post_id",
      "created_at",
      "comment",
      [
        sequelize.literal(`
      (SELECT CASE WHEN EXISTS (
        SELECT is_deleted
        FROM follow
        WHERE follower_id = ${id} AND following_id = hiring_post_comment.user_id AND is_deleted = false
      ) THEN 1 ELSE 0 END)
    `),
        "is_following",
      ],
      [
        sequelize.literal(`
        (SELECT CASE WHEN hiring_post_comment.user_id = ${id} THEN 1 ELSE 0 END)
      `),
        "is_current_user",
      ],
    ],
    include: {
      model: tables.User,
      attributes: ["name", "email", "profile_link"],
    },
    order: [["id", "desc"]],
  });
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data: {
      interest_count: likeCountQuery,
      comment_count: commentCountQuery,
      is_interested: isLikeQuery ? isLikeQuery.is_interested : false,
      data,
    },
  });
});

exports.editHiringPost = asyncHandler(async (req, res) => {
  let { id } = req.user;
  let { hiring_post_id } = req.params;
  let { post_media, post_media_delete_ids } = req.body;
  await tables.HiringPost.update(req.body, {
    where: { id: hiring_post_id },
  });

  if (post_media_delete_ids) {
    await tables.HiringPostMedia.update(
      { is_deleted: true },
      {
        where: { id: post_media_delete_ids },
      }
    );
  }
  if (post_media) {
    let postMediaArray = [];
    for (let i = 0; i < post_media.length; i++) {
      postMediaArray.push({
        media_link: post_media[i].media_link,
        user_id: id,
        hiring_post_id,
        media_type: post_media[i].media_type,
      });
    }
    await tables.HiringPostMedia.bulkCreate(postMediaArray);
  }
  return res.send({
    status: true,
    statusCode: 200,
    message: "hiring_post updated",
  });
});

exports.deleteHiringPost = asyncHandler(async (req, res) => {
  let { hiring_post_id } = req.params;

  await tables.HiringPost.update(
    { is_deleted: true },
    { where: { id: hiring_post_id } }
  );
  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
  });
});
