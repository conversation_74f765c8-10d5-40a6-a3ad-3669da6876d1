let { checkValidation } = require("../validation");
const { Op } = require("sequelize");
const bcrypt = require("bcryptjs");
const { tables, sequelize } = require("../db/index");
const jwt = require("jsonwebtoken");
const config = require("../constant");
const { asyncHandler } = require("../utils/catchError");
const AppError = require("../utils/appError");
const nodemailer = require("nodemailer");

function generateOtp() {
  // if (process.env.DUMMY_OTP) {
  //   return process.env.DUMMY_OTP;
  // } else {
  const otp = Array.from({ length: 4 }, () => Math.floor(Math.random() * 10));
  const otpString = otp.join("");
  return otpString;
  // }
}

const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.NODEMAILER_EMAIL,
    pass: process.env.NODEMAILER_PASSWORD,
  },
});

// Function to send an email
const sendMail = async (to, subject, text, html) => {
  try {
    const mailOptions = {
      from: "agrilala",
      to,
      subject,
      text,
      html,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log("Email sent: ", info.response);
  } catch (error) {
    console.error("Error sending email: ", error);
  }
};
exports.login = asyncHandler(async (req, res) => {
  const { mobile, password } = req.body;
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;

  // Validate mobile number format
  if (!validateMobileNumber(mobile)) {
    throw new AppError("Please enter a valid mobile number!", 400);
  }

  // Format mobile number with country code
  const formattedMobile = formatMobileNumber(mobile);

  let user = await tables.User.findOne({
    where: {
      mobile: formattedMobile,
      is_mobile_verified: true,
      user_type: req.params.type,
    },
    raw: true,
  });

  if (!user) {
    throw new AppError(
      "User not registered or mobile number not verified!",
      400
    );
  }
  const checkPassword = await bcrypt.compare(password, user.password);
  if (!checkPassword) {
    throw new AppError("Incorrect password or mobile number!", 400);
  }
  const token = jwt.sign(
    { id: user.id, mobile: user.mobile, user: req.params.type },
    config.USER_TOKEN_KEY,
    { expiresIn: "30d" }
  );
  if (!token) {
    throw new AppError("Something went wrong!", 400);
  }
  user.token = token;
  user.type = req.params.type;

  return res.send({
    status: true,
    statusCode: 200,
    user,
    message: "Login successful",
  });
});

exports.sendOTP = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  const { email } = req.body;
  let user = await tables.User.findOne({
    where: { email, user_type: req.params.type },
  });
  if (!user) {
    throw new AppError("email does not exist!", 400);
  }
  const otp = generateOtp();
  const optData = await tables.Otp.findOne({ where: { user_id: user.id } });
  if (optData) {
    await tables.Otp.update(
      { otp: otp, expires_at: Date.now() + 1000000 },
      { where: { user_id: user.id } }
    );
  } else {
    await tables.Otp.create({
      user_id: user.id,
      otp: otp,
      expires_at: Date.now() + 1000000,
      user_type: req.params.type,
    });
  }
  console.log(otp, "---");
  sendMail(
    email,
    "Your Otp Code",
    `Your Otp is: ${otp}`,
    `<h2>Your OTP Code</h2><p><strong>${otp}</strong></p>`
  );
  return res.send({
    status: true,
    statusCode: 200,
    message: "OTP sent successfully",
  });
});

exports.verifyOTP = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  const { otp, mobile } = req.body;

  // Validate mobile number format
  if (!validateMobileNumber(mobile)) {
    throw new AppError("Please enter a valid mobile number!", 400);
  }

  // Format mobile number with country code
  const formattedMobile = formatMobileNumber(mobile);

  let user = await tables.User.findOne({
    where: { mobile: formattedMobile, user_type: req.params.type },
  });
  if (!user) {
    throw new AppError("Mobile number does not exist!", 400);
  }

  const OTP = await tables.Otp.findOne({
    where: { user_id: user.id },
    raw: true,
    order: [["created_at", "DESC"]],
  });

  if (!OTP || otp !== OTP.otp) {
    throw new AppError("OTP does not match!", 400);
  } else if (Date.now() >= OTP.expires_at) {
    throw new AppError("OTP has expired!", 400);
  } else {
    await tables.User.update(
      { is_mobile_verified: true },
      { where: { mobile: formattedMobile } }
    );

    await tables.Otp.update({ expires_at: 0 }, { where: { user_id: user.id } });
    return res.send({
      status: true,
      statusCode: 200,
      message: "OTP verified successfully",
      user,
    });
  }
});

exports.getLocationDataByPincode = asyncHandler(async (req, res) => {
  const pincode = req.params.pincode;
  const data = await tables.Pincode.findOne({
    where: { pincode },
    include: [
      {
        model: tables.District,
        attributes: ["district_name"],
      },
      { model: tables.State, attributes: ["state_name"] },
      { model: tables.Taluka, attributes: ["taluka_name"] },
    ],
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "success",
    data,
  });
});

exports.setNewPassword = asyncHandler(async (req, res) => {
  let checkValidations = await checkValidation(req, res);
  if (checkValidations) return;
  const { mobile, password } = req.body;

  // Validate mobile number format
  if (!validateMobileNumber(mobile)) {
    throw new AppError("Please enter a valid mobile number!", 400);
  }

  // Format mobile number with country code
  const formattedMobile = formatMobileNumber(mobile);

  let user = await tables.User.findOne({
    where: {
      mobile: formattedMobile,
      user_type: req.params.type,
      is_mobile_verified: true,
    },
    raw: true,
  });

  if (!user) {
    throw new AppError("Mobile number does not exist or not verified!", 400);
  }
  const newPassword = await bcrypt.hash(password, 10);
  await tables.User.update(
    { password: newPassword },
    { where: { mobile: formattedMobile } }
  );
  // if (req.params.type == "COMPANY") {
  //   let company = await tables.Company.findOne({
  //     where: { user_id: user.id },
  //     raw: true,
  //   });
  //   user = { ...user, ...company };
  // }
  // if (req.params.type == "STAFF") {
  //   let staff = await tables.Staff.findOne({
  //     where: { user_id: user.id },
  //     raw: true,
  //   });
  //   user = { ...user, ...staff };
  //   console.log(staff, "staff dataaa");
  // }
  // if (req.params.type == "DEALER_OR_DISTRIBUTOR") {
  //   let firm = await tables.DealerDistributor.findOne({
  //     where: { user_id: user.id },
  //     raw: true,
  //   });
  //   user = { ...user, ...firm };
  // }
  console.log(user, "user data");
  const token = jwt.sign(
    { id: user.id, mobile: user.mobile, user: req.params.type },
    config.USER_TOKEN_KEY,
    { expiresIn: "30d" }
  );
  if (!token) {
    throw new AppError("something went wrong!", 400);
  }
  user.token = token;
  user.type = req.params.type;

  return res.send({
    status: true,
    statusCode: 200,
    message: "password created successfully",
    user,
  });
});

exports.getUserDetails = asyncHandler(async (req, res) => {
  const { id } = req.user;
  let user = await tables.User.findOne({
    where: { id },
    raw: true,
  });

  return res.send({
    status: true,
    statusCode: 200,
    message: "user details",
    user,
  });
});
