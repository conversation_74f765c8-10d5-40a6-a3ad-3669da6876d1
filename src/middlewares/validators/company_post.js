const { body } = require("express-validator");

exports.validateAddPost = [
  body("details")
    .exists()
    .withMessage("details is required")
    .notEmpty()
    .withMessage("details must be filled"),
  body("post_media")
    .exists()
    .withMessage("post_media is required")
    .notEmpty()
    .withMessage("post_media must be filled"),
];

exports.validatePostLike = [
  body("company_post_id")
    .exists()
    .withMessage("company_post_id is required")
    .notEmpty()
    .withMessage("company_post_id must be filled"),
  body("is_liked")
    .exists()
    .withMessage("is_liked is required")
    .notEmpty()
    .withMessage("is_liked must be filled"),
];
exports.validatePostComment = [
  body("company_post_id")
    .exists()
    .withMessage("company_post_id is required")
    .notEmpty()
    .withMessage("company_post_id must be filled"),
  body("comment")
    .exists()
    .withMessage("comment is required")
    .notEmpty()
    .withMessage("comment must be filled"),
];

exports.validatePostShare = [
  body("company_post_id")
    .exists()
    .withMessage("company_post_id is required")
    .notEmpty()
    .withMessage("company_post_id must be filled"),
];
