const { body } = require("express-validator");

exports.validateCompany = [
  body("name")
    .exists()
    .withMessage("company name is required")
    .notEmpty()
    .withMessage("name must be filled"),
  body("designation")
    .exists()
    .withMessage("designation is required")
    .notEmpty()
    .withMessage("designation must be filled"),
  body("mobile")
    .exists()
    .withMessage("mobile is required")
    .notEmpty()
    .withMessage("mobile must be filled")
    .isLength({ min: 10, max: 10 }),
  body("established_year")
    .exists()
    .withMessage("established year is required")
    .notEmpty()
    .withMessage("established year must be filled"),
  body("address")
    .exists()
    .withMessage("address is required")
    .notEmpty()
    .withMessage("address must be filled"),
  body("state_id")
    .exists()
    .withMessage("state id is required")
    .notEmpty()
    .withMessage("state_id must be filled"),
  body("district_id")
    .exists()
    .withMessage("district id is required")
    .notEmpty()
    .withMessage("district_id must be filled"),
  body("taluka_id")
    .exists()
    .withMessage("taluka id is required")
    .notEmpty()
    .withMessage("taluka_id must be filled"),
  body("pincode")
    .exists()
    .withMessage("pincode is required")
    .notEmpty()
    .withMessage("pincode must be filled"),
  body("r_p_name")
    .exists()
    .withMessage("r_p_name is required")
    .notEmpty()
    .withMessage("r_p_name must be filled"),
  body("user_type")
    .exists()
    .withMessage("user_type is required")
    .notEmpty()
    .withMessage("user_type must be filled"),
  body("email")
    .exists()
    .withMessage("email is required")
    .isEmail()
    .withMessage("Please enter valid email")
    .normalizeEmail(),
];
exports.validateAddExpense = [
  body("company_meet")
    .exists()
    .withMessage("company_meet is required in array")
    .notEmpty()
    .withMessage("company_meet must be filled"),
  body("company_expense")
    .exists()
    .withMessage("company_expense is required in array")
    .notEmpty()
    .withMessage("company_expense must be filled"),

  body("company_expense_details")
    .exists()
    .withMessage("company_expense_details is required in object")
    .notEmpty()
    .withMessage("company_expense_details must be filled"),

  body("company_id")
    .exists()
    .withMessage("company_id is required")
    .notEmpty()
    .withMessage("company_id must be filled"),
  body("expense_date")
    .exists()
    .withMessage("expense_date is required")
    .notEmpty()
    .withMessage("expense_date must be filled"),
];
