const { body } = require("express-validator");

exports.validateStaff = [
  body("name")
    .exists()
    .withMessage("staff name is required")
    .notEmpty()
    .withMessage("name must be filled"),
  body("company_name")
    .exists()
    .withMessage("company name is required")
    .notEmpty()
    .withMessage("company_name must be filled"),
  body("mobile")
    .exists()
    .withMessage("mobile is required")
    .notEmpty()
    .withMessage("mobile must be filled")
    .isLength({ min: 10, max: 10 }),
  body("email")
    .exists()
    .withMessage("email is required")
    .isEmail()
    .withMessage("Please enter valid email")
    .normalizeEmail(),
  body("designation")
    .exists()
    .withMessage("designation is required")
    .notEmpty()
    .withMessage("designation must be filled"),
  body("work_experience")
    .exists()
    .withMessage("work experience year is required")
    .notEmpty()
    .withMessage("work experience year must be filled"),
  body("posting_area")
    .exists()
    .withMessage("posting_area is required")
    .notEmpty()
    .withMessage("posting_area must be filled"),
  body("state_id")
    .exists()
    .withMessage("state id is required")
    .notEmpty()
    .withMessage("state_id must be filled"),
  body("district_id")
    .exists()
    .withMessage("district id is required")
    .notEmpty()
    .withMessage("district_id must be filled"),
  body("taluka_id")
    .exists()
    .withMessage("taluka id is required")
    .notEmpty()
    .withMessage("taluka_id must be filled"),
  body("pincode")
    .exists()
    .withMessage("pincode is required")
    .notEmpty()
    .withMessage("pincode must be filled"),
  body("user_type")
    .exists()
    .withMessage("user_type is required")
    .notEmpty()
    .withMessage("user_type must be filled"),
];
