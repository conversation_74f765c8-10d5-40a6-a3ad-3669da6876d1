const { body } = require("express-validator");

exports.validateLogin = [
  body("password")
    .exists()
    .withMessage("password is required")
    .notEmpty()
    .withMessage("password must be filled"),
  body("email")
    .exists()
    .withMessage("email is required")
    .isEmail()
    .withMessage("Please enter valid email")
    .normalizeEmail(),
];
exports.validateSendOTP = [
  body("email")
    .exists()
    .withMessage("email is required")
    .isEmail()
    .withMessage("Please enter valid email")
    .normalizeEmail(),
];
exports.validateVerifyOTP = [
  body("email")
    .exists()
    .withMessage("email is required")
    .isEmail()
    .withMessage("Please enter valid email")
    .normalizeEmail(),
  body("otp")
    .exists()
    .withMessage("otp is required")
    .notEmpty()
    .withMessage("OTP must be filled"),
];
