const { body } = require("express-validator");

exports.validateLogin = [
  body("password")
    .exists()
    .withMessage("password is required")
    .notEmpty()
    .withMessage("password must be filled"),
  body("email")
    .exists()
    .withMessage("email is required")
    .isEmail()
    .withMessage("Please enter valid email")
    .normalizeEmail(),
];
exports.validateSendOTP = [
  body("email")
    .exists()
    .withMessage("email is required")
    .isEmail()
    .withMessage("Please enter valid email")
    .normalizeEmail(),
];
exports.validateVerifyOTP = [
  body("is_mobile_verified")
    .exists()
    .isBoolean()
    .withMessage("is_mobile_verified is required"),
  body("mobile")
    .exists()
    .isLength({ min: 10, max: 10 })
    .withMessage("mobile is required"),
  // body("otp")
  //   .exists()
  //   .withMessage("otp is required")
  //   .notEmpty()
  //   .withMessage("OTP must be filled"),
];
