const { body } = require("express-validator");

exports.validateAddProduct = [
  body("product_name")
    .exists()
    .withMessage("product_name is required")
    .notEmpty()
    .withMessage("product_name must be filled"),
  body("description")
    .exists()
    .withMessage("description is required")
    .notEmpty()
    .withMessage("description must be filled"),
  body("product_media")
    .exists()
    .withMessage("product_media is required")
    .notEmpty()
    .withMessage("product_media must be filled"),
];

exports.validateProductLike = [
  body("product_id")
    .exists()
    .withMessage("product_id is required")
    .notEmpty()
    .withMessage("product_id must be filled"),
  body("is_liked")
    .exists()
    .withMessage("is_liked is required")
    .notEmpty()
    .withMessage("is_liked must be filled"),
];
exports.validateProductComment = [
  body("product_id")
    .exists()
    .withMessage("product_id is required")
    .notEmpty()
    .withMessage("product_id must be filled"),
  body("comment")
    .exists()
    .withMessage("comment is required")
    .notEmpty()
    .withMessage("comment must be filled"),
];

exports.validateProductShare = [
  body("product_id")
    .exists()
    .withMessage("product_id is required")
    .notEmpty()
    .withMessage("product_id must be filled"),
];
