const { body } = require("express-validator");

exports.validateAddHiringPost = [
  body("position")
    .exists()
    .withMessage("position is required")
    .notEmpty()
    .withMessage("position must be filled"),
  body("salary")
    .exists()
    .withMessage("salary is required")
    .notEmpty()
    .withMessage("salary must be filled"),
  body("location")
    .exists()
    .withMessage("location is required")
    .notEmpty()
    .withMessage("location must be filled"),
  body("description")
    .exists()
    .withMessage("description is required")
    .notEmpty()
    .withMessage("description must be filled"),
  body("post_media")
    .exists()
    .withMessage("post_media is required")
    .notEmpty()
    .withMessage("post_media must be filled"),
];

exports.validateHiringPostInterest = [
  body("hiring_post_id")
    .exists()
    .withMessage("hiring_post_id is required")
    .notEmpty()
    .withMessage("hiring_post_id must be filled"),
  body("is_interested")
    .exists()
    .withMessage("is_interested is required")
    .notEmpty()
    .withMessage("is_interested must be filled"),
];
exports.validateHiringPostComment = [
  body("hiring_post_id")
    .exists()
    .withMessage("hiring_post_id is required")
    .notEmpty()
    .withMessage("hiring_post_id must be filled"),
  body("comment")
    .exists()
    .withMessage("comment is required")
    .notEmpty()
    .withMessage("comment must be filled"),
];

exports.validateHiringPostShare = [
  body("hiring_post_id")
    .exists()
    .withMessage("hiring_post_id is required")
    .notEmpty()
    .withMessage("hiring_post_id must be filled"),
];
