const { body } = require("express-validator");

exports.validateAddCompanyLeadPost = [
  body("company_id")
    .exists()
    .withMessage("company_id is required")
    .notEmpty()
    .withMessage("company_id must be filled"),
  body("title")
    .exists()
    .withMessage("title is required")
    .notEmpty()
    .withMessage("title must be filled"),
  body("product_name")
    .exists()
    .withMessage("product_name is required")
    .notEmpty()
    .withMessage("product_name must be filled"),
  body("category")
    .exists()
    .withMessage("category is required")
    .notEmpty()
    .withMessage("category must be filled"),
  body("location")
    .exists()
    .withMessage("location is required")
    .notEmpty()
    .withMessage("location must be filled"),
  body("details")
    .exists()
    .withMessage("details is required")
    .notEmpty()
    .withMessage("details must be filled"),
  body("company_lead_post_media")
    .exists()
    .withMessage("company_lead_post_media is required")
    .notEmpty()
    .withMessage("company_lead_post_media must be filled"),
];

exports.validateCompanyLeadPostLike = [
  body("company_lead_id")
    .exists()
    .withMessage("company_lead_id is required")
    .notEmpty()
    .withMessage("company_lead_id must be filled"),
  body("is_liked")
    .exists()
    .withMessage("is_liked is required")
    .notEmpty()
    .withMessage("is_liked must be filled"),
];

exports.validateCompanyLeadPostComment = [
  body("company_lead_id")
    .exists()
    .withMessage("company_lead_id is required")
    .notEmpty()
    .withMessage("company_lead_id must be filled"),
  body("comment")
    .exists()
    .withMessage("comment is required")
    .notEmpty()
    .withMessage("comment must be filled"),
];

exports.validateCompanyLeadPostShare = [
  body("company_lead_id")
    .exists()
    .withMessage("company_lead_id is required")
    .notEmpty()
    .withMessage("company_lead_id must be filled"),
];
