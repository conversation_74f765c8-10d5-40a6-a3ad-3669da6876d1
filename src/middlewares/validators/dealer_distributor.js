const { body } = require("express-validator");

exports.validateDealerDistributor = [
  body("name")
    .exists()
    .withMessage("firm name is required")
    .notEmpty()
    .withMessage("name must be filled"),
  body("address")
    .exists()
    .withMessage("address is required")
    .notEmpty()
    .withMessage("address must be filled"),
  body("r_p_name")
    .exists()
    .withMessage("r_p_name is required")
    .notEmpty()
    .withMessage("r_p_name must be filled"),
  body("state_id")
    .exists()
    .withMessage("state id is required")
    .notEmpty()
    .withMessage("state_id must be filled"),
  body("district_id")
    .exists()
    .withMessage("district id is required")
    .notEmpty()
    .withMessage("district_id must be filled"),
  body("taluka_id")
    .exists()
    .withMessage("taluka id is required")
    .notEmpty()
    .withMessage("taluka_id must be filled"),
  body("pincode")
    .exists()
    .withMessage("pincode is required")
    .notEmpty()
    .withMessage("pincode must be filled"),
  body("established_year")
    .exists()
    .withMessage("established year is required")
    .notEmpty()
    .withMessage("established year must be filled"),
  body("firm_type")
    .exists()
    .withMessage("firm_type (DEALER/DISTRIBUTOR) is required")
    .notEmpty()
    .withMessage("firm_type (DEALER/DISTRIBUTOR) must be filled"),
  body("mobile")
    .exists()
    .withMessage("mobile is required")
    .notEmpty()
    .withMessage("mobile must be filled")
    .isLength({ min: 10, max: 10 }),
  body("email")
    .exists()
    .withMessage("email is required")
    .isEmail()
    .withMessage("Please enter valid email")
    .normalizeEmail(),
  body("user_type")
    .exists()
    .withMessage("user_type is required")
    .notEmpty()
    .withMessage("user_type must be filled"),
];

exports.validateBuyerRequirements = [
  body("field")
    .exists()
    .withMessage("field is required")
    .notEmpty()
    .withMessage("field must be filled"),
  body("product_name")
    .exists()
    .withMessage("product_name is required")
    .notEmpty()
    .withMessage("product_name must be filled"),
  body("category")
    .exists()
    .withMessage("category is required")
    .notEmpty()
    .withMessage("category must be filled"),
];

exports.validateBuyerRequirementLike = [
  body("buyer_requirement_id")
    .exists()
    .withMessage("buyer_requirement_id is required")
    .notEmpty()
    .withMessage("buyer_requirement_id must be filled"),
  body("is_liked")
    .exists()
    .withMessage("is_liked is required")
    .notEmpty()
    .withMessage("is_liked must be filled"),
];

exports.validateBuyerRequirementCall = [
  body("buyer_requirement_id")
    .exists()
    .withMessage("buyer_requirement_id is required")
    .notEmpty()
    .withMessage("buyer_requirement_id must be filled"),
];

exports.validateBuyerRequirementComment = [
  body("buyer_requirement_id")
    .exists()
    .withMessage("buyer_requirement_id is required")
    .notEmpty()
    .withMessage("buyer_requirement_id must be filled"),
  body("comment")
    .exists()
    .withMessage("comment is required")
    .notEmpty()
    .withMessage("comment must be filled"),
];

exports.validateBuyerRequirementActiveInactive = [
  body("is_active")
    .exists()
    .withMessage("is_active is required")
    .notEmpty()
    .withMessage("is_active must be filled"),
];
