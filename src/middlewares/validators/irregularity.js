const { body } = require("express-validator");

exports.validateAddIrregularity = [
  body("name")
    .exists()
    .withMessage("name is required")
    .notEmpty()
    .withMessage("name must be filled"),
  body("user_type")
    .exists()
    .withMessage("user_type is required")
    .notEmpty()
    .withMessage("user_type must be STAFF or DEALER_OR_DISTRIBUTOR"),
  body("note")
    .exists()
    .withMessage("note is required")
    .notEmpty()
    .withMessage("note must be filled"),
  body("mobile")
    .exists()
    .withMessage("mobile is required")
    .notEmpty()
    .withMessage("mobile must be filled"),
  body("city")
    .exists()
    .withMessage("city is required")
    .notEmpty()
    .withMessage("city must be filled"),
  body("district")
    .exists()
    .withMessage("district is required")
    .notEmpty()
    .withMessage("district must be filled"),
  body("taluka")
    .exists()
    .withMessage("taluka is required")
    .notEmpty()
    .withMessage("taluka must be filled"),
  body("state")
    .exists()
    .withMessage("state is required")
    .notEmpty()
    .withMessage("state must be filled"),
];

exports.validateIrregularityLike = [
  body("irregularity_id")
    .exists()
    .withMessage("irregularity_id is required")
    .notEmpty()
    .withMessage("irregularity_id must be filled"),
  body("is_liked")
    .exists()
    .withMessage("is_liked is required")
    .notEmpty()
    .withMessage("is_liked must be filled"),
];

exports.validateIrregularityComment = [
  body("irregularity_id")
    .exists()
    .withMessage("irregularity_id is required")
    .notEmpty()
    .withMessage("irregularity_id must be filled"),
  body("comment")
    .exists()
    .withMessage("comment is required")
    .notEmpty()
    .withMessage("comment must be filled"),
];
