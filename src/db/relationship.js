const { sequelize } = require("./models");

const Company = sequelize.import("./company");
const Otp = sequelize.import("./otp");
const District = sequelize.import("./district");
const Pincode = sequelize.import("./pincode");
const State = sequelize.import("./state");
const Taluka = sequelize.import("./taluka");
const Staff = sequelize.import("./staff");
const DealerDistributor = sequelize.import("./dealer_distributor");

const Post = sequelize.import("./post");
const PostMedia = sequelize.import("./post_media");
const PostLike = sequelize.import("./post_like");
const PostComment = sequelize.import("./post_comment");
const PostShare = sequelize.import("./post_share");

const Follow = sequelize.import("./follow");
const User = sequelize.import("./user");
const Subscription = sequelize.import("./subscription");
const UserSubscription = sequelize.import("./user_subscription");
const TeamMember = sequelize.import("./team_member");
const StaffLocation = sequelize.import("./staff_location");
const BuyerTool = sequelize.import("./buyer_requirement");
const BuyerRequirementLike = sequelize.import("./buyer_requirement_like");
const BuyerRequirementCall = sequelize.import("./buyer_requirement_call");
const BuyerRequirementMedia = sequelize.import("./buyer_requirement_media");
const BuyerField = sequelize.import("./buyer_field");
const BuyerRequirementComment = sequelize.import("./buyer_requirement_comment");
const Irregularity = sequelize.import("./irregularity");
const IrregularityComment = sequelize.import("./irregularity_comment");
const IrregularityLike = sequelize.import("./irregularity_like");

const Product = sequelize.import("./product");
const ProductMedia = sequelize.import("./product_media");
const ProductLike = sequelize.import("./product_like");
const ProductComment = sequelize.import("./product_comment");
const ProductShare = sequelize.import("./product_share");

const HiringPost = sequelize.import("./hiring_post");
const HiringPostMedia = sequelize.import("./hiring_post_media");
const HiringPostInterest = sequelize.import("./hiring_post_interest");
const HiringPostComment = sequelize.import("./hiring_post_comment");
const HiringPostShare = sequelize.import("./hiring_post_share");

const CompanyLeadPost = sequelize.import("./company_lead");
const CompanyLeadPostMedia = sequelize.import("./company_lead_media");
const CompanyLeadPostLike = sequelize.import("./company_lead_like");
const CompanyLeadPostComment = sequelize.import("./company_lead_comment");
const CompanyLeadPostShare = sequelize.import("./company_lead_share");

const CompanyPost = sequelize.import("./company_post");
const CompanyPostMedia = sequelize.import("./company_post_media");
const CompanyPostLike = sequelize.import("./company_post_like");
const CompanyPostComment = sequelize.import("./company_post_comment");
const CompanyPostShare = sequelize.import("./company_post_share");
const StaffCompany = sequelize.import("./staff_company");

const CompanyExpensesDetails = sequelize.import("./company_expense_detail");
const CompanyExpense = sequelize.import("./company_expense");
const CompanyMeet = sequelize.import("./company_meet");

State.hasMany(District, { foreignKey: "state_id" });
District.belongsTo(State, { foreignKey: "state_id" });

State.hasMany(Taluka, { foreignKey: "state_id" });
Taluka.belongsTo(State, { foreignKey: "state_id" });

State.hasMany(Pincode, { foreignKey: "state_id" });
Pincode.belongsTo(State, { foreignKey: "state_id" });

District.hasMany(Pincode, { foreignKey: "district_id" });
Pincode.belongsTo(District, { foreignKey: "district_id" });

Taluka.hasMany(Pincode, { foreignKey: "taluka_id" });
Pincode.belongsTo(Taluka, { foreignKey: "taluka_id" });

User.hasMany(Post, { foreignKey: "user_id" });
Post.belongsTo(User, { foreignKey: "user_id" });

User.hasMany(PostLike, { foreignKey: "user_id" });
PostLike.belongsTo(User, { foreignKey: "user_id" });

User.hasMany(PostComment, { foreignKey: "user_id" });
PostComment.belongsTo(User, { foreignKey: "user_id" });

Post.hasMany(PostMedia, { foreignKey: "post_id" });
PostMedia.belongsTo(Post, { foreignKey: "post_id" });

Subscription.hasMany(UserSubscription, { foreignKey: "subscription_id" });
UserSubscription.belongsTo(Subscription, { foreignKey: "subscription_id" });

User.hasMany(TeamMember, { foreignKey: "member_id", as: "member" });
TeamMember.belongsTo(User, { foreignKey: "member_id", as: "member" });

User.hasMany(TeamMember, { foreignKey: "user_id", as: "user" });
TeamMember.belongsTo(User, { foreignKey: "user_id", as: "user" });

User.hasMany(StaffLocation, { foreignKey: "user_id" });
StaffLocation.belongsTo(User, { foreignKey: "user_id" });

BuyerTool.hasMany(BuyerRequirementMedia, {
  foreignKey: "buyer_requirement_id",
});

User.hasMany(BuyerTool, { foreignKey: "user_id" });
BuyerTool.belongsTo(User, { foreignKey: "user_id" });

BuyerRequirementMedia.belongsTo(BuyerTool, {
  foreignKey: "buyer_requirement_id",
});

User.hasMany(BuyerRequirementComment, { foreignKey: "user_id" });
BuyerRequirementComment.belongsTo(User, { foreignKey: "user_id" });

User.hasMany(BuyerRequirementLike, { foreignKey: "user_id" });
BuyerRequirementLike.belongsTo(User, { foreignKey: "user_id" });

User.hasMany(Irregularity, { foreignKey: "inform_by", as: "inform_by_user" });
Irregularity.belongsTo(User, { foreignKey: "inform_by", as: "inform_by_user" });

User.hasMany(IrregularityComment, { foreignKey: "user_id" });
IrregularityComment.belongsTo(User, { foreignKey: "user_id" });

User.hasMany(IrregularityLike, { foreignKey: "user_id" });
IrregularityLike.belongsTo(User, { foreignKey: "user_id" });

User.hasMany(Product, { foreignKey: "user_id" });
Product.belongsTo(User, { foreignKey: "user_id" });

User.hasMany(ProductLike, { foreignKey: "user_id" });
ProductLike.belongsTo(User, { foreignKey: "user_id" });

User.hasMany(ProductComment, { foreignKey: "user_id" });
ProductComment.belongsTo(User, { foreignKey: "user_id" });

Product.hasMany(ProductMedia, { foreignKey: "product_id" });
ProductMedia.belongsTo(Product, { foreignKey: "product_id" });

User.hasMany(HiringPost, { foreignKey: "user_id" });
HiringPost.belongsTo(User, { foreignKey: "user_id" });

User.hasMany(HiringPostInterest, { foreignKey: "user_id" });
HiringPostInterest.belongsTo(User, { foreignKey: "user_id" });

User.hasMany(HiringPostComment, { foreignKey: "user_id" });
HiringPostComment.belongsTo(User, { foreignKey: "user_id" });

HiringPost.hasMany(HiringPostMedia, { foreignKey: "hiring_post_id" });
HiringPostMedia.belongsTo(HiringPost, { foreignKey: "hiring_post_id" });

//company lead
User.hasMany(CompanyLeadPost, { foreignKey: "user_id" });
CompanyLeadPost.belongsTo(User, { foreignKey: "user_id" });
User.hasMany(CompanyLeadPostLike, { foreignKey: "user_id" });
CompanyLeadPostLike.belongsTo(User, { foreignKey: "user_id" });
User.hasMany(CompanyLeadPostComment, { foreignKey: "user_id" });
CompanyLeadPostComment.belongsTo(User, { foreignKey: "user_id" });
CompanyLeadPost.hasMany(CompanyLeadPostMedia, {
  foreignKey: "company_lead_id",
});
CompanyLeadPostMedia.belongsTo(CompanyLeadPost, {
  foreignKey: "company_lead_id",
});

// company post
User.hasMany(CompanyPost, { foreignKey: "user_id" });
CompanyPost.belongsTo(User, { foreignKey: "user_id" });

User.hasMany(CompanyPostLike, { foreignKey: "user_id" });
CompanyPostLike.belongsTo(User, { foreignKey: "user_id" });

User.hasMany(CompanyPostComment, { foreignKey: "user_id" });
CompanyPostComment.belongsTo(User, { foreignKey: "user_id" });

CompanyPost.hasMany(CompanyPostMedia, { foreignKey: "company_post_id" });
CompanyPostMedia.belongsTo(CompanyPost, { foreignKey: "company_post_id" });

module.exports = {
  Company,
  Otp,
  District,
  Pincode,
  State,
  Taluka,
  Staff,
  DealerDistributor,
  Post,
  PostMedia,
  PostLike,
  PostComment,
  Follow,
  User,
  PostShare,
  Subscription,
  UserSubscription,
  TeamMember,
  StaffLocation,
  BuyerTool,
  BuyerRequirementLike,
  BuyerRequirementCall,
  BuyerRequirementMedia,
  BuyerField,
  BuyerRequirementComment,
  Irregularity,
  IrregularityComment,
  IrregularityLike,
  Product,
  ProductMedia,
  ProductLike,
  ProductComment,
  ProductShare,
  HiringPost,
  HiringPostMedia,
  HiringPostInterest,
  HiringPostComment,
  HiringPostShare,
  CompanyLeadPost,
  CompanyLeadPostMedia,
  CompanyLeadPostLike,
  CompanyLeadPostComment,
  CompanyLeadPostShare,
  StaffCompany,
  CompanyPost,
  CompanyPostMedia,
  CompanyPostLike,
  CompanyPostComment,
  CompanyPostShare,
  CompanyExpensesDetails,
  CompanyExpense,
  CompanyMeet,
};
