const { timestampHook } = require("../hooks");

module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "hiring_post",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      position: {
        type: DataTypes.STRING,
      },
      salary: {
        type: DataTypes.STRING(500),
      },
      location: {
        type: DataTypes.STRING,
      },
      description: {
        type: DataTypes.STRING,
      },
      user_id: {
        type: DataTypes.BIGINT,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.BIGINT,
      },
      updated_at: {
        type: DataTypes.BIGINT,
      },
    },
    {
      tableName: "hiring_post",
      timestamps: false,
      hooks: timestampHook,
    }
  );
};
