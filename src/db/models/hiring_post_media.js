const { timestampHook } = require("../hooks");

module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "hiring_post_media",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.BIGINT,
      },
      hiring_post_id: {
        type: DataTypes.BIGINT,
      },
      media_type: {
        type: DataTypes.ENUM("IMAGE", "VIDEO"),
      },
      media_link: {
        type: DataTypes.STRING,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.BIGINT,
      },
      updated_at: {
        type: DataTypes.BIGINT,
      },
    },
    {
      tableName: "hiring_post_media",
      timestamps: false,
      hooks: timestampHook,
    }
  );
};
