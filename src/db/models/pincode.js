const { timestampHook } = require("../hooks");

module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "pincode",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      pincode: {
        type: DataTypes.STRING(60),
      },
      state_id: {
        type: DataTypes.BIGINT,
      },
      district_id: {
        type: DataTypes.BIGINT,
      },
      taluka_id: {
        type: DataTypes.BIGINT,
      },
      is_del: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.BIGINT,
      },
      updated_at: {
        type: DataTypes.BIGINT,
      },
    },
    {
      tableName: "pincode",
      timestamps: false,
      hooks: timestampHook,
    }
  );
};
