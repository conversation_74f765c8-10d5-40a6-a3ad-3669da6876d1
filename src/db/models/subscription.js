const { timestampHook } = require("../hooks");

module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "subscription",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      frequency: {
        type: DataTypes.STRING(60),
      },
      subscription_detail: {
        type: DataTypes.STRING,
      },
      subscription_price: {
        type: DataTypes.STRING(60),
      },
      subscription_members: {
        type: DataTypes.STRING(60),
      },
      is_plus_member: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      is_del: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.BIGINT,
      },
      updated_at: {
        type: DataTypes.BIGINT,
      },
    },
    {
      tableName: "subscription",
      timestamps: false,
      hooks: timestampHook,
    }
  );
};
