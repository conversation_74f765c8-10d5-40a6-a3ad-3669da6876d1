const { timestampHook } = require("../hooks");

module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "staff",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.BIGINT,
      },
      company_name: {
        type: DataTypes.STRING(100),
      },
      mobile: {
        type: DataTypes.BIGINT,
      },
      designation: {
        type: DataTypes.STRING(100),
      },
      work_experience: {
        type: DataTypes.STRING(50),
      },
      posting_area: {
        type: DataTypes.STRING(100),
      },
      state_id: {
        type: DataTypes.BIGINT,
      },
      district_id: {
        type: DataTypes.BIGINT,
      },
      taluka_id: {
        type: DataTypes.BIGINT,
      },
      pincode: {
        type: DataTypes.BIGINT,
      },
      user_name: {
        type: DataTypes.STRING,
      },
      bio: {
        type: DataTypes.STRING,
      },
      cover_image: {
        type: DataTypes.STRING,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.BIGINT,
      },
      updated_at: {
        type: DataTypes.BIGINT,
      },
    },
    {
      tableName: "staff",
      timestamps: false,
      hooks: timestampHook,
    }
  );
};
