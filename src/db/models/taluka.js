const { timestampHook } = require("../hooks");

module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "taluka",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      taluka_name: {
        type: DataTypes.STRING(100),
      },
      district_id: {
        type: DataTypes.BIGINT,
      },
      state_id: {
        type: DataTypes.BIGINT,
      },
      is_del: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.BIGINT,
      },
      updated_at: {
        type: DataTypes.BIGINT,
      },
    },
    {
      tableName: "taluka",
      timestamps: false,
      hooks: timestampHook,
    }
  );
};
