const { timestampHook } = require("../hooks");

module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "dealer_distributor",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.BIGINT,
      },
      address: {
        type: DataTypes.STRING,
      },
      r_p_name: {
        type: DataTypes.STRING(100),
      },
      district_id: {
        type: DataTypes.BIGINT,
      },
      taluka_id: {
        type: DataTypes.BIGINT,
      },
      pincode: {
        type: DataTypes.BIGINT,
      },
      state_id: {
        type: DataTypes.BIGINT,
      },
      established_year: {
        type: DataTypes.STRING(50),
      },
      mobile: {
        type: DataTypes.BIGINT,
      },
      firm_type: {
        type: DataTypes.ENUM("DEALER", "DISTRIBUTOR"),
        allowNull: false,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.BIGINT,
      },
      updated_at: {
        type: DataTypes.BIGINT,
      },
    },
    {
      tableName: "dealer_distributor",
      timestamps: false,
      hooks: timestampHook,
    }
  );
};
