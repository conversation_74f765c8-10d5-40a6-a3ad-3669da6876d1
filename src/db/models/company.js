const { timestampHook } = require("../hooks");

module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "company",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.BIGINT,
      },
      r_p_name: {
        type: DataTypes.STRING(100),
      },
      designation: {
        type: DataTypes.STRING(100),
      },
      mobile: {
        type: DataTypes.BIGINT,
      },
      established_year: {
        type: DataTypes.STRING(50),
      },
      address: {
        type: DataTypes.STRING,
      },
      state_id: {
        type: DataTypes.BIGINT,
      },
      district_id: {
        type: DataTypes.BIGINT,
      },
      taluka_id: {
        type: DataTypes.BIGINT,
      },
      pincode: {
        type: DataTypes.BIGINT,
      },
      is_admin_approved: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.BIGINT,
      },
      updated_at: {
        type: DataTypes.BIGINT,
      },
    },
    {
      tableName: "company",
      timestamps: false,
      hooks: timestampHook,
    }
  );
};
