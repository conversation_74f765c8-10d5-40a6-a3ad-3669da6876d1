const { timestampHook } = require("../hooks");

module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "user",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: DataTypes.STRING(100),
      },
      email: {
        type: DataTypes.STRING(100),
        unique: true,
        allowNull: false,
      },
      mobile: {
        type: DataTypes.STRING(15),
        unique: true,
        allowNull: true,
      },
      password: {
        type: DataTypes.STRING,
      },
      user_type: {
        type: DataTypes.ENUM("STAFF", "COMPANY", "DEALER_OR_DISTRIBUTOR"),
        allowNull: false,
      },
      profile_link: {
        type: DataTypes.STRING,
      },
      is_email_verified: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      is_mobile_verified: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.BIGINT,
      },
      updated_at: {
        type: DataTypes.BIGINT,
      },
    },
    {
      tableName: "user",
      timestamps: false,
      hooks: timestampHook,
    }
  );
};
