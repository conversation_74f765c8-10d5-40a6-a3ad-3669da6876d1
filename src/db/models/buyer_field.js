const { timestampHook } = require("../hooks");

module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "buyer_field",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: DataTypes.STRING,
      },
      is_del: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.BIGINT,
      },
      updated_at: {
        type: DataTypes.BIGINT,
      },
    },
    {
      tableName: "buyer_field",
      timestamps: false,
      hooks: timestampHook,
    }
  );
};
