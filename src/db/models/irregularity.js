const { timestampHook } = require("../hooks");

module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "irregularity",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      user_id: {
        type: DataTypes.BIGINT,
      },
      name: {
        type: DataTypes.STRING,
      },
      mobile: {
        type: DataTypes.BIGINT,
      },
      inform_by: {
        type: DataTypes.BIGINT,
      },
      firm_name: {
        type: DataTypes.STRING,
      },
      user_type: {
        type: DataTypes.ENUM("STAFF", "DEALER_OR_DISTRIBUTOR"),
        allowNull: false,
      },
      user_photo: {
        type: DataTypes.STRING,
      },
      city: {
        type: DataTypes.STRING,
      },
      district: {
        type: DataTypes.STRING,
      },
      taluka: {
        type: DataTypes.STRING,
      },
      state: {
        type: DataTypes.STRING,
      },
      note: {
        type: DataTypes.STRING,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.BIGINT,
      },
      updated_at: {
        type: DataTypes.BIGINT,
      },
    },
    {
      tableName: "irregularity",
      timestamps: false,
      hooks: timestampHook,
    }
  );
};
