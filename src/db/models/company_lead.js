const { timestampHook } = require("../hooks");

module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "company_lead",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      company_id: {
        type: DataTypes.BIGINT,
      },
      title: {
        type: DataTypes.STRING,
      },
      details: {
        type: DataTypes.STRING(500),
      },
      product_name: {
        type: DataTypes.STRING(500),
      },
      category: {
        type: DataTypes.STRING(500),
      },
      location: {
        type: DataTypes.STRING(500),
      },
      user_id: {
        type: DataTypes.BIGINT,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.BIGINT,
      },
      updated_at: {
        type: DataTypes.BIGINT,
      },
    },
    {
      tableName: "company_lead",
      timestamps: false,
      hooks: timestampHook,
    }
  );
};
