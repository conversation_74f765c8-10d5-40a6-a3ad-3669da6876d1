const { timestampHook } = require("../hooks");

module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "company_expense_detail",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      company_id: {
        type: DataTypes.BIGINT,
      },
      user_id: {
        type: DataTypes.BIGINT,
      },
      total_amount: {
        type: DataTypes.DECIMAL(10, 2),
      },
      total_driven_kilometer: {
        type: DataTypes.STRING,
      },
      notes: {
        type: DataTypes.STRING,
      },
      expense_date: {
        type: DataTypes.STRING,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.BIGINT,
      },
      updated_at: {
        type: DataTypes.BIGINT,
      },
    },
    {
      tableName: "company_expense_detail",
      timestamps: false,
      hooks: timestampHook,
    }
  );
};
