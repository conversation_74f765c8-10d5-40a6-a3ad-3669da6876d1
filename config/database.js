module.exports = {
  development: {
    host: process.env.DB_LOCAL_HOST,
    username: process.env.DB_LOCAL_USER,
    password: process.env.DB_LOCAL_PASSWORD,
    database: process.env.DB_LOCAL_DATABASE,
    dialect: process.env.DB_LOCAL_DIALECT,
  },
  staging: {
    username: process.env.DB_LOCAL_USER,
    password: process.env.DB_LOCAL_PASSWORD,
    database: process.env.DB_LOCAL_DATABASE,
    dialect: process.env.DB_LOCAL_DIALECT,
  },
  production: {
    username: process.env.DB_LOCAL_USER,
    password: process.env.DB_LOCAL_PASSWORD,
    database: process.env.DB_LOCAL_DATABASE,
    dialect: process.env.DB_LOCAL_DIALECT,
  },
};
